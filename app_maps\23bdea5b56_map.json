{"url": "https://delightful-river-0e8d6780f.1.azurestaticapps.net/", "discovered_at": "2025-09-01 16:50:10", "pages": {"login": {"url": "https://delightful-river-0e8d6780f.1.azurestaticapps.net/login", "title": "regulatory compliance", "elements": {"buttons": [{"text": "Sign In", "xpath": "//*[@id=\"root\"]/DIV[2]/DIV[1]/DIV[2]/FORM[1]/BUTTON[1]", "classes": "inline-flex items-center justify-center gap-2 whitespace-nowrap text-sm ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0 bg-primary hover:bg-primary/90 px-4 py-2 w-full h-12 rounded-xl bg-gradient-to-r from-orange-500 via-orange-600 to-orange-700 hover:from-orange-600 hover:via-orange-700 hover:to-orange-800 text-white font-semibold shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-[1.02] disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none", "id": ""}], "links": [], "inputs": [{"type": "text", "placeholder": "Username", "name": "", "xpath": "//*[@id=\"root\"]/DIV[2]/DIV[1]/DIV[2]/FORM[1]/DIV[1]/INPUT[1]", "id": ""}, {"type": "password", "placeholder": "Password", "name": "", "xpath": "//*[@id=\"root\"]/DIV[2]/DIV[1]/DIV[2]/FORM[1]/DIV[2]/INPUT[1]", "id": ""}], "forms": [], "navigation": [], "tables": [], "metrics": []}}}}