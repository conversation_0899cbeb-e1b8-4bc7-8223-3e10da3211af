import streamlit as st
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import ElementNotInteractableException, TimeoutException, NoSuchElementException
import time
import json
import re
from transformers import AutoTokenizer, AutoModelForCausalLM

# -------------------------------
# Load Falcon model (cached)
# -------------------------------
@st.cache_resource
def load_model():
    tokenizer = AutoTokenizer.from_pretrained("tiiuae/Falcon3-1B-Instruct")
    model = AutoModelForCausalLM.from_pretrained(
        "tiiuae/Falcon3-1B-Instruct",
        device_map="auto",
        trust_remote_code=True
    )
    return tokenizer, model

tokenizer, model = load_model()

# -------------------------------
# XPath Configuration
# -------------------------------
NAVIGATION_BUTTONS = {
    'regulatory_hub': {
        'xpath': '//*[@id="root"]/div[2]/div/aside/div/nav/a[1]/button/span',
        'name': 'Regulatory Hub',
        'alternate_xpaths': [
            '//*[@id="root"]/div[2]/div/aside/div/nav/a[1]/button',
            '//*[@id="root"]/div[2]/div/aside/div/nav/a[1]',
            "//button[contains(., 'Regulatory Hub')]",
            "//span[contains(text(), 'Regulatory Hub')]"
        ]
    },
    'regulatory_analysis': {
        'xpath': '//*[@id="root"]/div[2]/div/aside/div/nav/a[2]/button/span',
        'name': 'Regulatory Analysis',
        'alternate_xpaths': [
            '//*[@id="root"]/div[2]/div/aside/div/nav/a[2]/button',
            '//*[@id="root"]/div[2]/div/aside/div/nav/a[2]',
            "//button[contains(., 'Regulatory Analysis')]",
            "//span[contains(text(), 'Regulatory Analysis')]"
        ]
    }
}

# Page-specific elements to test
PAGE_ELEMENTS = {
    'regulatory_hub': {
        'indicators': [
            "//div[contains(text(), 'Admin Panel')]",
            "//h2[contains(text(), 'Admin Panel')]",
            "//div[contains(text(), 'Total Users')]",
            "//div[contains(text(), 'Total Queries')]",
            "//div[contains(text(), 'Average Response Time')]",
            "//div[contains(text(), 'Query Success Rate')]",
            "//div[contains(text(), 'Active Users')]",
            "//th[contains(text(), 'Username')]",
            "//th[contains(text(), 'Status')]",
            "//input[contains(@placeholder, 'Search users')]"
        ],
        'test_elements': [
            "//button[contains(text(), 'Refresh')]",
            "//button[contains(text(), 'Export')]",
            "//button[contains(text(), 'Add User')]",
            "//input[@placeholder='Search users by name, username or email']",
            "//select | //div[@role='combobox']"
        ]
    },
    'regulatory_analysis': {
        'indicators': [
            "//h1[contains(text(), 'Welcome to')]",
            "//span[contains(text(), 'Regulatory Analysis')]",
            "//div[contains(text(), 'navigate complex regulatory')]",
            "//div[contains(text(), 'help you navigate')]",
            "//input[@placeholder='Ask any compliance question']",
            "//textarea[@placeholder='Ask any compliance question']",
            "//div[contains(text(), 'RECENT CONVERSATIONS')]"
        ],
        'test_elements': [
            "//input[@placeholder='Ask any compliance question']",
            "//textarea[@placeholder='Ask any compliance question']",
            "//button[@type='submit']",
            "//button[contains(text(), 'Submit')]",
            "//button[contains(text(), 'Send')]"
        ]
    }
}

# -------------------------------
# Helper Functions
# -------------------------------
def ask_model(prompt):
    """Get AI model response"""
    messages = [{"role": "user", "content": prompt}]
    inputs = tokenizer.apply_chat_template(
        messages,
        add_generation_prompt=True,
        tokenize=True,
        return_dict=True,
        return_tensors="pt",
    ).to(model.device)

    outputs = model.generate(**inputs, max_new_tokens=300, do_sample=True)
    raw_output = tokenizer.decode(
        outputs[0][inputs["input_ids"].shape[-1]:],
        skip_special_tokens=True
    )
    return raw_output.strip()

def extract_json(response):
    """Extract JSON from model response"""
    try:
        cleaned = re.sub(r"```(?:json)?", "", response)
        cleaned = cleaned.replace("```", "").strip()
        match = re.search(r"\{.*\}", cleaned, re.DOTALL)
        if match:
            return json.loads(match.group(0))
        return {"action": "none"}
    except:
        return {"action": "none"}

def click_element_by_xpath(driver, xpath, description="element"):
    """Click an element using XPath with multiple fallback methods"""
    try:
        element = WebDriverWait(driver, 10).until(
            EC.element_to_be_clickable((By.XPATH, xpath))
        )
        
        # Scroll element into view
        driver.execute_script("arguments[0].scrollIntoView({block: 'center'});", element)
        time.sleep(0.5)
        
        # Try different click methods
        try:
            element.click()
            return True, f"✅ Clicked {description}"
        except:
            # Try JavaScript click
            driver.execute_script("arguments[0].click();", element)
            return True, f"✅ Clicked {description} (JS)"
    except TimeoutException:
        return False, f"⏱️ Timeout waiting for {description}"
    except Exception as e:
        return False, f"❌ Error clicking {description}: {str(e)[:50]}"

def detect_current_page(driver):
    """Detect which page we're currently on"""
    # Wait a bit for page to stabilize
    time.sleep(1)
    
    # Check URL first
    current_url = driver.current_url.lower()
    
    # Check for Regulatory Hub page indicators (Admin Panel is unique to Hub)
    hub_indicators = [
        "//div[contains(text(), 'Admin Panel')]",
        "//h2[contains(text(), 'Admin Panel')]",
        "//div[contains(text(), 'Active Users')]",
        "//th[contains(text(), 'Username')]",
        "//th[contains(text(), 'Role')]",
        "//th[contains(text(), 'Status')]",
        "//input[contains(@placeholder, 'Search users')]"
    ]
    
    for indicator in hub_indicators:
        try:
            element = driver.find_element(By.XPATH, indicator)
            if element and element.is_displayed():
                return 'regulatory_hub'
        except:
            continue
    
    # Check for Regulatory Analysis page indicators
    analysis_indicators = [
        "//h1[contains(text(), 'Welcome to')]",
        "//span[contains(text(), 'Regulatory Analysis')]",
        "//div[contains(text(), 'navigate complex regulatory')]",
        "//div[contains(text(), 'help you navigate')]",
        "//input[contains(@placeholder, 'Ask any compliance question')]",
        "//textarea[contains(@placeholder, 'Ask any compliance question')]",
        "//div[contains(text(), 'RECENT CONVERSATIONS')]"
    ]
    
    for indicator in analysis_indicators:
        try:
            element = driver.find_element(By.XPATH, indicator)
            if element and element.is_displayed():
                return 'regulatory_analysis'
        except:
            continue
    
    # Additional checks based on URL patterns
    if 'hub' in current_url or 'admin' in current_url or 'dashboard' in current_url:
        return 'regulatory_hub'
    elif 'analysis' in current_url or 'compliance' in current_url:
        return 'regulatory_analysis'
    
    # Check which navigation button is active/selected
    try:
        # Check if Hub button has active class
        hub_button = driver.find_element(By.XPATH, '//*[@id="root"]/div[2]/div/aside/div/nav/a[1]')
        hub_classes = hub_button.get_attribute('class') or ''
        if 'active' in hub_classes.lower() or 'selected' in hub_classes.lower():
            return 'regulatory_hub'
    except:
        pass
    
    try:
        # Check if Analysis button has active class
        analysis_button = driver.find_element(By.XPATH, '//*[@id="root"]/div[2]/div/aside/div/nav/a[2]')
        analysis_classes = analysis_button.get_attribute('class') or ''
        if 'active' in analysis_classes.lower() or 'selected' in analysis_classes.lower():
            return 'regulatory_analysis'
    except:
        pass
    
    return 'unknown'

def navigate_to_page(driver, target_page, logs):
    """Navigate to a specific page using the sidebar buttons"""
    initial_page = detect_current_page(driver)
    
    if initial_page == target_page:
        logs.append(f"✅ Already on {NAVIGATION_BUTTONS[target_page]['name']}")
        return True
    
    logs.append(f"🔄 Navigating from {initial_page} to {NAVIGATION_BUTTONS[target_page]['name']}")
    
    # Store initial URL to detect changes
    initial_url = driver.current_url
    
    # Try clicking the navigation button
    nav_button = NAVIGATION_BUTTONS[target_page]
    
    # Try primary XPath first
    try:
        # First try to click the parent anchor tag
        if target_page == 'regulatory_hub':
            parent_xpath = '//*[@id="root"]/div[2]/div/aside/div/nav/a[1]'
        else:
            parent_xpath = '//*[@id="root"]/div[2]/div/aside/div/nav/a[2]'
        
        element = WebDriverWait(driver, 10).until(
            EC.element_to_be_clickable((By.XPATH, parent_xpath))
        )
        driver.execute_script("arguments[0].click();", element)
        logs.append(f"✅ Clicked {nav_button['name']} navigation link")
        
        # Wait for page to load
        time.sleep(3)
        
        # Wait for URL change or page content change
        try:
            WebDriverWait(driver, 10).until(
                lambda d: d.current_url != initial_url or detect_current_page(d) != initial_page
            )
        except:
            pass
        
        # Check if navigation was successful
        new_page = detect_current_page(driver)
        if new_page == target_page:
            logs.append(f"🎉 Successfully navigated to {nav_button['name']}")
            return True
        else:
            logs.append(f"⚠️ Navigation result: landed on {new_page} instead of {target_page}")
            
    except Exception as e:
        logs.append(f"⚠️ Could not click navigation link: {str(e)[:50]}")
    
    # If that didn't work, try the button or span directly
    for xpath in [nav_button['xpath']] + nav_button['alternate_xpaths']:
        try:
            element = driver.find_element(By.XPATH, xpath)
            driver.execute_script("arguments[0].scrollIntoView({block: 'center'});", element)
            time.sleep(0.5)
            
            # Force click using JavaScript
            driver.execute_script("arguments[0].click();", element)
            logs.append(f"✅ Clicked element with XPath: {xpath[:50]}...")
            
            # Wait for navigation
            time.sleep(3)
            
            # Check if we reached the target
            new_page = detect_current_page(driver)
            if new_page == target_page:
                logs.append(f"🎉 Successfully navigated to {nav_button['name']}")
                return True
                
        except Exception as e:
            continue
    
    # Final check
    final_page = detect_current_page(driver)
    if final_page == target_page:
        logs.append(f"🎉 Navigation successful - now on {nav_button['name']}")
        return True
    
    logs.append(f"❌ Failed to navigate to {target_page}. Current page: {final_page}")
    return False

def test_regulatory_hub(driver, logs):
    """Test Regulatory Hub specific features"""
    logs.append("🧪 Testing Regulatory Hub features...")
    
    # Check for dashboard metrics
    metrics = [
        ('Total Users', '31'),
        ('Total Queries', '1,078'),
        ('Average Response Time', '49.2s'),
        ('Last Index Refresh', 'days ago'),
        ('Query Success Rate', '%')
    ]
    
    for metric_name, metric_hint in metrics:
        try:
            # Try to find the metric by text
            element = driver.find_element(By.XPATH, f"//div[contains(text(), '{metric_name}')]")
            if element.is_displayed():
                logs.append(f"✅ Found metric: {metric_name}")
                # Try to find the value
                try:
                    value_elem = driver.find_element(By.XPATH, f"//div[contains(text(), '{metric_hint}')]")
                    logs.append(f"  📊 Value contains: {metric_hint}")
                except:
                    pass
        except:
            logs.append(f"⚠️ Metric not found: {metric_name}")
    
    # Check for Admin Panel
    try:
        admin_panel = driver.find_element(By.XPATH, "//div[contains(text(), 'Admin Panel')] | //h2[contains(text(), 'Admin Panel')]")
        if admin_panel.is_displayed():
            logs.append("✅ Found Admin Panel section")
    except:
        logs.append("⚠️ Admin Panel section not found")
    
    # Test user search functionality
    try:
        search_input = driver.find_element(By.XPATH, "//input[contains(@placeholder, 'Search users')]")
        if search_input.is_displayed():
            search_input.clear()
            search_input.send_keys("test")
            logs.append("✅ Entered text in user search field")
            time.sleep(1)
            search_input.clear()
    except:
        logs.append("⚠️ User search input not found")
    
    # Check for user table headers
    table_headers = ['Name', 'Username', 'Email', 'Role', 'Status', 'Created', 'Actions']
    headers_found = 0
    for header in table_headers:
        try:
            header_elem = driver.find_element(By.XPATH, f"//th[contains(text(), '{header}')] | //div[@role='columnheader'][contains(text(), '{header}')]")
            if header_elem.is_displayed():
                headers_found += 1
        except:
            pass
    
    if headers_found > 0:
        logs.append(f"✅ Found {headers_found}/{len(table_headers)} table headers")
    
    # Check for user entries
    try:
        # Look for Admin role badges or user rows
        user_elements = driver.find_elements(By.XPATH, "//div[contains(text(), 'Admin')] | //div[contains(text(), 'Active')] | //td[contains(@class, 'user')]")
        if user_elements:
            logs.append(f"✅ Found {len(user_elements)} user-related elements")
        
        # Check for specific test users from screenshot
        test_users = ['pwc test', 'rakeshm', 'rav']
        for user in test_users:
            try:
                user_elem = driver.find_element(By.XPATH, f"//td[contains(text(), '{user}')] | //div[contains(text(), '{user}')]")
                if user_elem.is_displayed():
                    logs.append(f"  ✅ Found user: {user}")
            except:
                pass
                
    except:
        logs.append("⚠️ Could not check user table")
    
    # Check for action buttons
    try:
        action_buttons = driver.find_elements(By.XPATH, "//button[contains(text(), 'Refresh')] | //button[contains(text(), 'Export')] | //button[contains(text(), 'Add User')]")
        if action_buttons:
            logs.append(f"✅ Found {len(action_buttons)} action buttons")
    except:
        pass
    
    return logs

def test_regulatory_analysis(driver, logs):
    """Test Regulatory Analysis specific features"""
    logs.append("🧪 Testing Regulatory Analysis features...")
    
    # Test the compliance question input
    test_questions = [
        "What are GDPR requirements?",
        "How to comply with HIPAA?",
        "What is SOC 2 compliance?"
    ]
    
    # Try multiple selectors for the input field
    input_selectors = [
        "//input[@placeholder='Ask any compliance question']",
        "//textarea[@placeholder='Ask any compliance question']",
        "//input[contains(@placeholder, 'compliance')]",
        "//textarea[contains(@placeholder, 'compliance')]",
        "//input[@type='text']",
        "//textarea"
    ]
    
    input_found = False
    for selector in input_selectors:
        try:
            input_field = driver.find_element(By.XPATH, selector)
            if input_field.is_displayed():
                input_found = True
                
                # Test with one question
                question = test_questions[0]
                input_field.clear()
                input_field.send_keys(question)
                logs.append(f"✅ Entered question: {question}")
                time.sleep(1)
                
                # Try to find and click submit button
                submit_selectors = [
                    "//button[@type='submit']",
                    "//button[contains(text(), 'Send')]",
                    "//button[contains(text(), 'Ask')]",
                    "//button[contains(text(), 'Submit')]",
                    "//button[contains(@class, 'submit')]",
                    "//button[following-sibling::*[contains(text(), 'compliance')] or preceding-sibling::*[contains(text(), 'compliance')]]"
                ]
                
                for submit_selector in submit_selectors:
                    try:
                        submit_button = driver.find_element(By.XPATH, submit_selector)
                        if submit_button.is_displayed() and submit_button.is_enabled():
                            submit_button.click()
                            logs.append("✅ Clicked submit button")
                            time.sleep(2)
                            break
                    except:
                        continue
                
                # Clear the input for next test
                try:
                    input_field.clear()
                except:
                    pass
                
                break
        except:
            continue
    
    if not input_found:
        logs.append("⚠️ Compliance question input not found")
    
    # Check for recent conversations in the sidebar
    try:
        conversations = driver.find_elements(By.XPATH, "//div[contains(text(), 'What')]")
        if conversations:
            logs.append(f"✅ Found {len(conversations)} recent conversations")
        else:
            # Try alternative selector
            conv_alt = driver.find_elements(By.XPATH, "//*[contains(@class, 'conversation') or contains(@class, 'chat')]")
            logs.append(f"📊 Found {len(conv_alt)} conversation elements")
    except:
        logs.append("⚠️ Could not check recent conversations")
    
    return logs

def detect_login_page(driver):
    """Detect if we're on a login page"""
    login_indicators = [
        "//input[@type='email']",
        "//input[@type='password']",
        "//input[contains(@placeholder, 'Username')]",
        "//input[contains(@placeholder, 'Email')]",
        "//button[contains(text(), 'Sign In')]",
        "//button[contains(text(), 'Login')]"
    ]
    
    for indicator in login_indicators:
        try:
            element = driver.find_element(By.XPATH, indicator)
            if element.is_displayed():
                return True
        except:
            continue
    
    return False

def perform_login(driver, username, password, logs):
    """Perform login if on login page"""
    if not detect_login_page(driver):
        logs.append("ℹ️ Not on login page, skipping login")
        return True
    
    logs.append("🔐 Login page detected, attempting login...")
    
    try:
        # Find and fill username/email field
        username_field = driver.find_element(By.XPATH, 
            "//input[@type='email'] | //input[@type='text'][contains(@placeholder, 'Username')] | //input[@type='text'][contains(@placeholder, 'Email')]")
        username_field.clear()
        username_field.send_keys(username)
        logs.append(f"✅ Entered username: {username}")
        
        # Find and fill password field
        password_field = driver.find_element(By.XPATH, "//input[@type='password']")
        password_field.clear()
        password_field.send_keys(password)
        logs.append("✅ Entered password")
        
        # Click login button
        login_button = driver.find_element(By.XPATH, 
            "//button[contains(text(), 'Sign In')] | //button[contains(text(), 'Login')] | //button[@type='submit']")
        login_button.click()
        logs.append("✅ Clicked login button")
        
        time.sleep(3)
        
        # Check if login was successful
        if not detect_login_page(driver):
            logs.append("✅ Login successful")
            return True
        else:
            logs.append("❌ Login failed - still on login page")
            return False
            
    except Exception as e:
        logs.append(f"❌ Login error: {str(e)[:100]}")
        return False

# -------------------------------
# Streamlit UI
# -------------------------------
st.set_page_config(page_title="Regulatory App Testing", page_icon="🤖", layout="wide")

st.title("🤖 Automated Testing for Regulatory Application")
st.markdown("Automated Selenium testing for Regulatory Hub and Regulatory Analysis pages")

# Application Configuration
st.markdown("### 🔗 Application Configuration")
col1, col2 = st.columns(2)

with col1:
    app_url = st.text_input("Application URL:", placeholder="https://your-app-url.com", help="Enter the URL of your application")

with col2:
    st.markdown("<br>", unsafe_allow_html=True)
    require_login = st.checkbox("Application requires login", value=True)

# Login Credentials
if require_login:
    st.markdown("### 🔐 Login Credentials")
    col3, col4 = st.columns(2)
    
    with col3:
        username = st.text_input("Username/Email:", help="Enter your username or email")
    
    with col4:
        password = st.text_input("Password:", type="password", help="Enter your password")
else:
    username = password = None

# Test Configuration
st.markdown("### ⚙️ Test Configuration")
col5, col6, col7 = st.columns(3)

with col5:
    test_mode = st.selectbox(
        "Test Mode:",
        ["Complete Test Suite", "Navigation Test Only", "Regulatory Hub Only", "Regulatory Analysis Only"]
    )

with col6:
    show_browser = st.checkbox("Show browser window", value=False, help="Enable for debugging")
    capture_screenshots = st.checkbox("Capture screenshots", value=True)

with col7:
    test_depth = st.selectbox("Test Depth:", ["Quick (Basic)", "Standard", "Comprehensive"])
    wait_time = st.slider("Wait time between actions (seconds):", 1, 5, 2)

# XPath Configuration Display
with st.expander("📍 XPath Configuration", expanded=False):
    st.code(f"""
Navigation Buttons:
- Regulatory Hub: {NAVIGATION_BUTTONS['regulatory_hub']['xpath']}
- Regulatory Analysis: {NAVIGATION_BUTTONS['regulatory_analysis']['xpath']}

Page Detection: Using page-specific indicators and elements
    """)

# Start Testing Button
if st.button("🚀 Start Testing", type="primary", use_container_width=True):
    if not app_url:
        st.error("⚠️ Please enter the application URL")
    elif require_login and (not username or not password):
        st.error("⚠️ Please enter login credentials")
    else:
        # Initialize progress tracking
        progress_placeholder = st.empty()
        status_placeholder = st.empty()
        
        # Initialize WebDriver
        status_placeholder.info("🚀 Initializing browser...")
        
        try:
            chrome_options = Options()
            if not show_browser:
                chrome_options.add_argument("--headless=new")
            chrome_options.add_argument("--disable-gpu")
            chrome_options.add_argument("--no-sandbox")
            chrome_options.add_argument("--disable-dev-shm-usage")
            chrome_options.add_argument("--window-size=1920,1080")
            
            driver = webdriver.Chrome(options=chrome_options)
            driver.get(app_url)
            time.sleep(wait_time)
            
        except Exception as e:
            st.error(f"🚨 Error launching browser: {e}")
            st.stop()
        
        logs = []
        screenshots = []
        visited_pages = set()
        test_results = {'passed': 0, 'failed': 0, 'warnings': 0}
        
        # Perform login if required
        if require_login:
            status_placeholder.info("🔐 Performing login...")
            login_success = perform_login(driver, username, password, logs)
            if not login_success:
                st.error("❌ Login failed. Please check credentials.")
                driver.quit()
                st.stop()
            time.sleep(wait_time)
        
        # Detect initial page
        initial_page = detect_current_page(driver)
        logs.append(f"📍 Initial page: {initial_page}")
        visited_pages.add(initial_page)
        
        if capture_screenshots:
            driver.save_screenshot("screenshot_initial.png")
            screenshots.append("screenshot_initial.png")
        
        # Execute test based on selected mode
        total_steps = 6 if test_depth == "Quick (Basic)" else 10 if test_depth == "Standard" else 15
        progress_bar = progress_placeholder.progress(0)
        
        if test_mode == "Complete Test Suite":
            # Test Regulatory Hub
            status_placeholder.info("📊 Testing Regulatory Hub...")
            progress_bar.progress(0.25)
            
            if navigate_to_page(driver, 'regulatory_hub', logs):
                visited_pages.add('regulatory_hub')
                test_regulatory_hub(driver, logs)
                if capture_screenshots:
                    driver.save_screenshot("screenshot_hub.png")
                    screenshots.append("screenshot_hub.png")
                time.sleep(wait_time)
            
            # Test Regulatory Analysis
            status_placeholder.info("📝 Testing Regulatory Analysis...")
            progress_bar.progress(0.5)
            
            if navigate_to_page(driver, 'regulatory_analysis', logs):
                visited_pages.add('regulatory_analysis')
                test_regulatory_analysis(driver, logs)
                if capture_screenshots:
                    driver.save_screenshot("screenshot_analysis.png")
                    screenshots.append("screenshot_analysis.png")
                time.sleep(wait_time)
            
            # Test navigation between pages
            status_placeholder.info("🔄 Testing navigation...")
            progress_bar.progress(0.75)
            
            for i in range(2):
                target = 'regulatory_hub' if i % 2 == 0 else 'regulatory_analysis'
                navigate_to_page(driver, target, logs)
                time.sleep(wait_time)
        
        elif test_mode == "Navigation Test Only":
            status_placeholder.info("🔄 Testing navigation between pages...")
            for i in range(4):
                progress_bar.progress((i + 1) / 4)
                target = 'regulatory_hub' if i % 2 == 0 else 'regulatory_analysis'
                if navigate_to_page(driver, target, logs):
                    visited_pages.add(target)
                    if capture_screenshots:
                        driver.save_screenshot(f"screenshot_nav_{i}.png")
                        screenshots.append(f"screenshot_nav_{i}.png")
                time.sleep(wait_time)
        
        elif test_mode == "Regulatory Hub Only":
            status_placeholder.info("📊 Testing Regulatory Hub...")
            if navigate_to_page(driver, 'regulatory_hub', logs):
                visited_pages.add('regulatory_hub')
                test_regulatory_hub(driver, logs)
        
        elif test_mode == "Regulatory Analysis Only":
            status_placeholder.info("📝 Testing Regulatory Analysis...")
            if navigate_to_page(driver, 'regulatory_analysis', logs):
                visited_pages.add('regulatory_analysis')
                test_regulatory_analysis(driver, logs)
        
        progress_bar.progress(1.0)
        
        # Get final page
        final_page = detect_current_page(driver)
        logs.append(f"📍 Final page: {final_page}")
        
        # Close browser
        driver.quit()
        
        # Count results
        for log in logs:
            if "✅" in log:
                test_results['passed'] += 1
            elif "❌" in log:
                test_results['failed'] += 1
            elif "⚠️" in log:
                test_results['warnings'] += 1
        
        # Clear status placeholders
        progress_placeholder.empty()
        status_placeholder.empty()
        
        # Display Results
        st.success("✅ Testing completed successfully!")
        
        # Summary Metrics
        st.markdown("### 📊 Test Summary")
        col1, col2, col3, col4, col5 = st.columns(5)
        
        with col1:
            st.metric("Tests Passed", test_results['passed'], delta=None, delta_color="normal")
        
        with col2:
            st.metric("Tests Failed", test_results['failed'], delta=None, delta_color="inverse")
        
        with col3:
            st.metric("Warnings", test_results['warnings'], delta=None, delta_color="normal")
        
        with col4:
            st.metric("Pages Tested", len(visited_pages), delta=None, delta_color="normal")
        
        with col5:
            st.metric("Screenshots", len(screenshots), delta=None, delta_color="normal")
        
        # Page Coverage
        st.markdown("### 📄 Page Coverage")
        col1, col2 = st.columns(2)
        
        with col1:
            if 'regulatory_hub' in visited_pages:
                st.success("✅ **Regulatory Hub** - Tested Successfully")
                st.caption("Dashboard metrics, Admin panel, and user management features tested")
            else:
                st.error("❌ **Regulatory Hub** - Not Tested")
        
        with col2:
            if 'regulatory_analysis' in visited_pages:
                st.success("✅ **Regulatory Analysis** - Tested Successfully")
                st.caption("Compliance question input and submission features tested")
            else:
                st.error("❌ **Regulatory Analysis** - Not Tested")
        
        # Detailed Test Log
        st.markdown("### 📋 Detailed Test Log")
        
        # Create tabs for different log views
        tab1, tab2, tab3 = st.tabs(["All Logs", "Errors Only", "Successes Only"])
        
        with tab1:
            with st.expander("View Complete Test Log", expanded=True):
                for log in logs:
                    if "✅" in log:
                        st.success(log)
                    elif "❌" in log:
                        st.error(log)
                    elif "⚠️" in log:
                        st.warning(log)
                    elif "🎉" in log:
                        st.success(log)
                    elif "📍" in log or "🔄" in log:
                        st.info(log)
                    else:
                        st.write(log)
        
        with tab2:
            errors = [log for log in logs if "❌" in log or "⚠️" in log]
            if errors:
                for log in errors:
                    if "❌" in log:
                        st.error(log)
                    else:
                        st.warning(log)
            else:
                st.success("No errors found!")
        
        with tab3:
            successes = [log for log in logs if "✅" in log or "🎉" in log]
            for log in successes:
                st.success(log)
        
        # Test Report
        st.markdown("### 📈 Test Report")
        
        report = f"""
**Test Configuration:**
- Application URL: {app_url}
- Test Mode: {test_mode}
- Test Depth: {test_depth}
- Browser: {'Visible' if show_browser else 'Headless'}

**Results:**
- Total Tests: {test_results['passed'] + test_results['failed'] + test_results['warnings']}
- Passed: {test_results['passed']}
- Failed: {test_results['failed']}
- Warnings: {test_results['warnings']}
- Success Rate: {(test_results['passed'] / max(1, test_results['passed'] + test_results['failed']) * 100):.1f}%

**Pages Tested:**
- Regulatory Hub: {'✅ Yes' if 'regulatory_hub' in visited_pages else '❌ No'}
- Regulatory Analysis: {'✅ Yes' if 'regulatory_analysis' in visited_pages else '❌ No'}
        """
        
        st.text_area("Test Report (Copy for documentation):", report, height=300)
        
        # Download button for report
        st.download_button(
            label="📥 Download Test Report",
            data=report,
            file_name=f"test_report_{time.strftime('%Y%m%d_%H%M%S')}.txt",
            mime="text/plain"
        )