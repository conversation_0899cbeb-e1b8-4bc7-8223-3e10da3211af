import streamlit as st
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, NoSuchElementException, ElementNotInteractableException
from selenium.webdriver.common.keys import Keys
import time
import json
import re
from typing import Dict, List, Any, Optional
from dataclasses import dataclass, asdict
import hashlib
from pathlib import Path
from transformers import AutoTokenizer, AutoModelForCausalLM
import pandas as pd
import io
from datetime import datetime

# -------------------------------
# Load <PERSON>wen model (cached)
# -------------------------------
@st.cache_resource
def load_model():
    try:
        import torch
        
        tokenizer = AutoTokenizer.from_pretrained("Qwen/Qwen2-1.5B-Instruct")
        
        # Properly configure device and loading
        device = "cuda" if torch.cuda.is_available() else "cpu"
        
        model = AutoModelForCausalLM.from_pretrained(
            "Qwen/Qwen2-1.5B-Instruct",
            device_map="auto" if torch.cuda.is_available() else None,
            torch_dtype=torch.float16 if torch.cuda.is_available() else torch.float32,
            trust_remote_code=True,
            low_cpu_mem_usage=True
        )
        
        # Move model to correct device if needed
        if device == "cpu":
            model = model.to(device)
        
        return tokenizer, model, True  # Return success flag
    except Exception as e:
        st.warning(f"Could not load Qwen model: {str(e)[:100]}")
        return None, None, False  # Return failure flag

# Try to load model
tokenizer, model, model_loaded = load_model()

# -------------------------------
# AI Intelligence Helper
# -------------------------------
class AIHelper:
    """Uses Qwen model to make intelligent testing decisions"""
    
    def __init__(self):
        self.tokenizer = tokenizer
        self.model = model
        self.available = model_loaded  # Check if model loaded successfully
    
    def ask_model(self, prompt: str) -> str:
        """Get AI model response"""
        if not self.available:
            return ""
        
        try:
            import torch
            
            messages = [{"role": "user", "content": prompt}]
            inputs = self.tokenizer.apply_chat_template(
                messages,
                add_generation_prompt=True,
                tokenize=True,
                return_dict=True,
                return_tensors="pt",
            )
            
            # Ensure inputs are on the same device as model
            if hasattr(self.model, 'device'):
                inputs = inputs.to(self.model.device)
            
            with torch.no_grad():
                outputs = self.model.generate(**inputs, max_new_tokens=300, do_sample=True)
            
            raw_output = self.tokenizer.decode(
                outputs[0][inputs["input_ids"].shape[-1]:],
                skip_special_tokens=True
            )
            return raw_output.strip()
        except Exception as e:
            print(f"AI error: {str(e)}")
            return ""
    
    def extract_json(self, response: str) -> Dict:
        """Extract JSON from model response"""
        try:
            cleaned = re.sub(r"```(?:json)?", "", response)
            cleaned = cleaned.replace("```", "").strip()
            match = re.search(r"\{.*\}", cleaned, re.DOTALL)
            if match:
                return json.loads(match.group(0))
            return {"action": "none"}
        except:
            return {"action": "none"}
    
    def generate_test_value_ai(self, input_type: str, placeholder: str = "", name: str = "") -> str:
        """Use AI to generate smart test data or fallback to defaults"""
        if not self.available:
            # Use default values if AI not available
            return self._get_default_value(input_type)
        
        try:
            prompt = f"""
            Generate appropriate test data for this input field:
            Type: {input_type}
            Placeholder: {placeholder}
            Field name: {name}
            
            Respond with JSON:
            {{
                "test_value": "appropriate test value"
            }}
            """
            
            response = self.ask_model(prompt)
            result = self.extract_json(response)
            
            # Fallback to default if AI fails
            if "test_value" in result:
                return result["test_value"]
        except:
            pass
        
        return self._get_default_value(input_type)
    
    def _get_default_value(self, input_type: str) -> str:
        """Get default test value for input type"""
        defaults = {
            "email": "<EMAIL>",
            "password": "Test123!@#",
            "text": "Test input",
            "number": "123",
            "tel": "555-0123",
            "url": "https://example.com",
            "search": "test search",
            "date": "2024-01-01",
            "time": "12:00"
        }
        return defaults.get(input_type, "test")
    
    def analyze_page(self, elements_summary: Dict) -> str:
        """AI analyzes the page and suggests what to test"""
        if not self.available:
            return "Standard web application"
        
        try:
            prompt = f"""
            Analyze this web page with these elements:
            - Buttons: {elements_summary.get('buttons', 0)}
            - Input fields: {elements_summary.get('inputs', 0)}
            - Links: {elements_summary.get('links', 0)}
            - Dropdowns: {elements_summary.get('dropdowns', 0)}
            
            What type of application is this likely to be?
            Respond with JSON:
            {{
                "app_type": "type of application",
                "priority": "what to test first"
            }}
            """
            
            response = self.ask_model(prompt)
            result = self.extract_json(response)
            
            return result.get("app_type", "Unknown application type")
        except:
            return "Standard web application"
    
    def should_click_button(self, button_text: str) -> bool:
        """AI decides if a button is safe to click"""
        dangerous_keywords = ["delete", "remove", "logout", "sign out", "cancel", "close", "exit", "clear"]
        button_lower = button_text.lower()
        
        for keyword in dangerous_keywords:
            if keyword in button_lower:
                return False
        
        return True
    
    def detect_page_type(self, page_content: str, elements_info: Dict) -> Dict:
        """AI detects the type of page and suggests appropriate actions"""
        if not self.available:
            return self._fallback_page_detection(page_content, elements_info)
        
        try:
            prompt = f"""
            Analyze this web page and determine its type and appropriate testing strategy:
            
            Page Content (first 500 chars): {page_content[:500]}
            
            Elements found:
            - Buttons: {elements_info.get('buttons', 0)}
            - Input fields: {elements_info.get('inputs', 0)}
            - Links: {elements_info.get('links', 0)}
            - Dropdowns: {elements_info.get('dropdowns', 0)}
            
            Determine:
            1. What type of page this is (login, chat, dashboard, form, etc.)
            2. Whether it needs specific credentials
            3. What kind of testing approach to use
            
            Respond with JSON:
            {{
                "page_type": "login|chat|dashboard|form|other",
                "needs_credentials": true/false,
                "testing_approach": "use_provided_credentials|generate_test_data|chat_interaction",
                "suggested_actions": ["action1", "action2"],
                "confidence": "high|medium|low"
            }}
            """
            
            response = self.ask_model(prompt)
            result = self.extract_json(response)
            
            # Validate and enhance result
            if not result.get("page_type"):
                result = self._fallback_page_detection(page_content, elements_info)
            
            return result
        except:
            return self._fallback_page_detection(page_content, elements_info)
    
    def _fallback_page_detection(self, page_content: str, elements_info: Dict) -> Dict:
        """Fallback detection when AI is not available"""
        page_lower = page_content.lower()
        
        # Enhanced chat interface detection
        chat_indicators = [
            "chat", "message", "conversation", "send", "reply", "ask", "question",
            "chatgpt", "openai", "assistant", "ai", "bot", "help", "support",
            "prompt", "input", "type your message", "what would you like to know",
            "how can i help", "ask me anything", "start a conversation"
        ]
        
        # Simple keyword-based detection
        if any(keyword in page_lower for keyword in ["login", "sign in", "log in", "password", "username"]):
            return {
                "page_type": "login",
                "needs_credentials": True,
                "testing_approach": "use_provided_credentials",
                "suggested_actions": ["fill_username", "fill_password", "click_submit"],
                "confidence": "medium"
            }
        elif any(keyword in page_lower for keyword in chat_indicators):
            return {
                "page_type": "chat",
                "needs_credentials": False,
                "testing_approach": "chat_interaction",
                "suggested_actions": ["generate_test_message", "send_message"],
                "confidence": "medium"
            }
        elif elements_info.get('inputs', 0) > 2:
            return {
                "page_type": "form",
                "needs_credentials": False,
                "testing_approach": "generate_test_data",
                "suggested_actions": ["fill_form_fields", "submit_form"],
                "confidence": "low"
            }
        else:
            return {
                "page_type": "other",
                "needs_credentials": False,
                "testing_approach": "generate_test_data",
                "suggested_actions": ["explore_elements"],
                "confidence": "low"
            }
    
    def generate_chat_message(self, context: str = "", page_content: str = "") -> str:
        """Generate appropriate chat message for testing based on context and page content"""
        if not self.available:
            return self._get_contextual_chat_message(page_content)
        
        try:
            # Analyze the page content to understand what type of chat interface this is
            page_lower = page_content.lower()
            chat_type = "general"
            
            if any(keyword in page_lower for keyword in ["chatgpt", "openai", "gpt"]):
                chat_type = "ai_assistant"
            elif any(keyword in page_lower for keyword in ["support", "help", "customer"]):
                chat_type = "customer_support"
            elif any(keyword in page_lower for keyword in ["bot", "automated", "faq"]):
                chat_type = "automated_bot"
            elif any(keyword in page_lower for keyword in ["chat", "messenger", "conversation"]):
                chat_type = "general_chat"
            
            prompt = f"""
            Generate a test message for a {chat_type} chat interface. The message should:
            - Be appropriate for testing the chat functionality
            - Be realistic and contextually relevant
            - Help verify that the chat system is working properly
            - Be clear and specific enough to test response capabilities
            - Not be offensive or inappropriate
            
            Context: {context}
            Page content indicators: {page_content[:200]}
            Chat type detected: {chat_type}
            
            Generate a message that would be appropriate for testing this type of chat interface.
            
            Respond with JSON:
            {{
                "message": "your test message here",
                "reasoning": "why this message is good for testing"
            }}
            """
            
            response = self.ask_model(prompt)
            result = self.extract_json(response)
            
            if "message" in result:
                return result["message"]
        except:
            pass
        
        return self._get_contextual_chat_message(page_content)
    
    def _get_contextual_chat_message(self, page_content: str = "") -> str:
        """Get contextual test messages based on page content"""
        import random
        
        page_lower = page_content.lower()
        
        # AI Assistant specific messages (like ChatGPT)
        if any(keyword in page_lower for keyword in ["chatgpt", "openai", "gpt", "ai assistant"]):
            ai_messages = [
                "What is the capital of France?",
                "Can you explain quantum computing in simple terms?",
                "Write a short poem about technology",
                "What are the benefits of renewable energy?",
                "How does machine learning work?",
                "Explain the water cycle",
                "What is the difference between AI and machine learning?",
                "Can you help me write a professional email?",
                "What are some tips for time management?",
                "Explain photosynthesis"
            ]
            return random.choice(ai_messages)
        
        # Customer Support messages
        elif any(keyword in page_lower for keyword in ["support", "help", "customer service", "assistance"]):
            support_messages = [
                "I need help with my account",
                "How do I reset my password?",
                "I'm having trouble logging in",
                "Can you help me with billing questions?",
                "I want to cancel my subscription",
                "How do I contact customer service?",
                "I have a technical issue",
                "Can you help me update my profile?",
                "I need assistance with my order",
                "How do I change my email address?"
            ]
            return random.choice(support_messages)
        
        # General chat messages
        elif any(keyword in page_lower for keyword in ["chat", "message", "conversation", "talk"]):
            general_messages = [
                "Hello, how are you today?",
                "What's the weather like?",
                "Can you tell me a joke?",
                "What's your favorite color?",
                "How was your day?",
                "What do you like to do for fun?",
                "Tell me something interesting",
                "What's the best advice you've ever received?",
                "Can you recommend a good book?",
                "What's your favorite season?"
            ]
            return random.choice(general_messages)
        
        # Default fallback messages
        else:
            default_messages = [
                "Hello, this is a test message",
                "Testing the chat functionality",
                "Can you help me with a test query?",
                "This is an automated test message",
                "Hi, I'm testing the system",
                "Is this chat working properly?",
                "Test message to verify functionality",
                "Hello, can you respond to this message?"
            ]
            return random.choice(default_messages)
    
    def generate_multiple_chat_messages(self, page_content: str = "", count: int = 3) -> List[str]:
        """Generate multiple test messages for comprehensive chat testing"""
        if not self.available:
            return [self._get_contextual_chat_message(page_content) for _ in range(count)]
        
        try:
            # Analyze the page content to understand what type of chat interface this is
            page_lower = page_content.lower()
            chat_type = "general"
            
            if any(keyword in page_lower for keyword in ["chatgpt", "openai", "gpt"]):
                chat_type = "ai_assistant"
            elif any(keyword in page_lower for keyword in ["support", "help", "customer"]):
                chat_type = "customer_support"
            elif any(keyword in page_lower for keyword in ["bot", "automated", "faq"]):
                chat_type = "automated_bot"
            elif any(keyword in page_lower for keyword in ["chat", "messenger", "conversation"]):
                chat_type = "general_chat"
            
            prompt = f"""
            Generate {count} different test messages for a {chat_type} chat interface. Each message should:
            - Be appropriate for testing the chat functionality
            - Be realistic and contextually relevant
            - Test different aspects of the chat system
            - Be clear and specific enough to test response capabilities
            - Not be offensive or inappropriate
            - Be diverse in nature (questions, requests, statements, etc.)
            
            Page content indicators: {page_content[:200]}
            Chat type detected: {chat_type}
            
            Generate {count} different messages that would be appropriate for testing this type of chat interface.
            
            Respond with JSON:
            {{
                "messages": ["message1", "message2", "message3"],
                "reasoning": "why these messages are good for testing"
            }}
            """
            
            response = self.ask_model(prompt)
            result = self.extract_json(response)
            
            if "messages" in result and isinstance(result["messages"], list):
                return result["messages"][:count]
        except:
            pass
        
        # Fallback to contextual messages
        return [self._get_contextual_chat_message(page_content) for _ in range(count)]
    
    def _get_default_chat_messages(self) -> str:
        """Get default test messages for chat (legacy method)"""
        return self._get_contextual_chat_message("")
    
    def determine_credential_strategy(self, page_type: str, provided_username: str = None, provided_password: str = None) -> Dict:
        """Determine what credentials to use based on page type and provided credentials"""
        if page_type == "login" and provided_username and provided_password:
            return {
                "use_provided": True,
                "username": provided_username,
                "password": provided_password,
                "reason": "Login page detected with provided credentials"
            }
        elif page_type == "chat":
            return {
                "use_provided": False,
                "username": None,
                "password": None,
                "reason": "Chat interface - no login credentials needed"
            }
        else:
            # Generate test credentials for other scenarios
            return {
                "use_provided": False,
                "username": "testuser",
                "password": "Test123!",
                "reason": f"Non-login page ({page_type}) - using default test credentials"
            }
class UniversalDiscoverer:
    """Discovers and interacts with ANY web application"""
    
    def __init__(self, driver):
        self.driver = driver
        self.discovered_elements = {
            "buttons": [],
            "links": [],
            "inputs": [],
            "dropdowns": [],
            "clickables": [],
            "forms": []
        }
        self.interactions_log = []
    
    def discover_all_elements(self) -> Dict:
        """Discover ALL interactive elements on current page"""
        
        # Find all buttons (multiple strategies)
        button_selectors = [
            "//button",
            "//input[@type='submit']",
            "//input[@type='button']",
            "//*[@role='button']",
            "//a[@class and contains(@class, 'btn')]"
        ]
        
        for selector in button_selectors:
            try:
                elements = self.driver.find_elements(By.XPATH, selector)
                for elem in elements:
                    if self._is_interactive(elem):
                        self.discovered_elements["buttons"].append({
                            "text": elem.text or elem.get_attribute("value") or elem.get_attribute("aria-label") or "Unnamed",
                            "element": elem,
                            "type": "button"
                        })
            except:
                continue
        
        # Find all input fields
        input_elements = self.driver.find_elements(By.XPATH, "//input | //textarea")
        for elem in input_elements:
            if self._is_interactive(elem):
                input_type = elem.get_attribute("type") or "text"
                if input_type not in ["submit", "button", "hidden"]:
                    self.discovered_elements["inputs"].append({
                        "type": input_type,
                        "placeholder": elem.get_attribute("placeholder") or "",
                        "name": elem.get_attribute("name") or "",
                        "element": elem
                    })
        
        # Find all dropdowns/selects
        select_elements = self.driver.find_elements(By.XPATH, "//select")
        for elem in select_elements:
            if self._is_interactive(elem):
                self.discovered_elements["dropdowns"].append({
                    "name": elem.get_attribute("name") or "Unnamed",
                    "element": elem
                })
        
        # Find all links
        link_elements = self.driver.find_elements(By.XPATH, "//a[@href]")
        for elem in link_elements:
            if self._is_interactive(elem):
                href = elem.get_attribute("href")
                if href and not href.startswith("#"):
                    self.discovered_elements["links"].append({
                        "text": elem.text or "Unnamed link",
                        "href": href,
                        "element": elem
                    })
        
        # Find any other clickable elements
        clickable_selectors = [
            "//*[@onclick]",
            "//*[@ng-click]",
            "//*[@data-click]",
            "//div[@role='link']",
            "//span[contains(@class, 'clickable')]"
        ]
        
        for selector in clickable_selectors:
            try:
                elements = self.driver.find_elements(By.XPATH, selector)
                for elem in elements:
                    if self._is_interactive(elem):
                        self.discovered_elements["clickables"].append({
                            "text": elem.text[:50] if elem.text else "Clickable element",
                            "element": elem
                        })
            except:
                continue
        
        return self.discovered_elements
    
    def _is_interactive(self, element) -> bool:
        """Check if element is visible and interactive"""
        try:
            return element.is_displayed() and element.is_enabled()
        except:
            return False
    
    def get_summary(self) -> Dict:
        """Get summary of discovered elements"""
        return {
            "buttons": len(self.discovered_elements["buttons"]),
            "inputs": len(self.discovered_elements["inputs"]),
            "dropdowns": len(self.discovered_elements["dropdowns"]),
            "links": len(self.discovered_elements["links"]),
            "clickables": len(self.discovered_elements["clickables"]),
            "total": sum([
                len(self.discovered_elements["buttons"]),
                len(self.discovered_elements["inputs"]),
                len(self.discovered_elements["dropdowns"]),
                len(self.discovered_elements["links"]),
                len(self.discovered_elements["clickables"])
            ])
        }

# -------------------------------
# Excel Template Handler
# -------------------------------
class ExcelTemplateHandler:
    """Handles Excel template generation and parsing for test cases"""
    
    def __init__(self):
        self.template_columns = [
            "Test Case ID",
            "Test Case Name", 
            "Description",
            "Action",
            "Target Element",
            "Value/Data",
            "Expected Result",
            "Priority",
            "Category"
        ]
    
    def generate_template(self) -> bytes:
        """Generate Excel template with empty columns for test scenarios"""
        
        # Create empty DataFrame with just the column headers
        empty_scenarios = [
            {
                "Test_ID": "",
                "Test_Name": "",
                "Test_Description": "",
                "Expected_Result": "",
                "Priority": "",
                "Username": "",
                "Password": ""
            }
        ]
        
        # Create DataFrame with empty data
        df = pd.DataFrame(empty_scenarios)
        
        # Create Excel file in memory
        output = io.BytesIO()
        with pd.ExcelWriter(output, engine='openpyxl') as writer:
            df.to_excel(writer, sheet_name='Test_Scenarios', index=False)
            
            # Get the workbook and worksheet
            workbook = writer.book
            worksheet = writer.sheets['Test_Scenarios']
            
            # Add instructions sheet with examples
            instructions_data = {
                'Column': ['Test_ID', 'Test_Name', 'Test_Description', 'Expected_Result', 'Priority', 'Username', 'Password'],
                'Description': [
                    'Unique identifier for the test scenario (e.g., TS001, TS002)',
                    'Brief name describing what the test does',
                    'Step-by-step description of what the test should do (use natural language)',
                    'What should happen as a result of this test',
                    'Priority level: High, Medium, Low',
                    'Username to use for this test (optional - leave empty for default)',
                    'Password to use for this test (optional - leave empty for default)'
                ],
                'Example_1': [
                    'TS001',
                    'User Login',
                    '1. Navigate to the application homepage\n2. Click on the login button\n3. Enter \'testuser\' in the username field\n4. Enter \'Test123!\' in the password field\n5. Click the submit button\n6. Wait for 2 seconds\n7. Verify that \'Dashboard\' text is displayed',
                    'User should be logged in and see dashboard',
                    'High',
                    '<EMAIL>',
                    'admin123'
                ],
                'Example_2': [
                    'TS002',
                    'Contact Form',
                    '1. Navigate to the contact page\n2. Fill in the name field with \'John Doe\'\n3. Enter email \'<EMAIL>\'\n4. Type a message \'This is a test message\'\n5. Click the submit button\n6. Wait for 3 seconds\n7. Verify that \'Thank you\' message appears',
                    'Contact form should be submitted successfully',
                    'Medium',
                    '',
                    ''
                ],
                'Example_3': [
                    'TS003',
                    'Product Search',
                    '1. Go to the homepage\n2. Click on the search box\n3. Type \'laptop\' in the search field\n4. Press Enter or click search button\n5. Wait for results to load\n6. Verify that search results are displayed',
                    'Search results should show products related to laptop',
                    'High',
                    '',
                    ''
                ]
            }
            
            instructions_df = pd.DataFrame(instructions_data)
            instructions_df.to_excel(writer, sheet_name='Instructions', index=False)
            
            # Format the main sheet
            from openpyxl.styles import Font, PatternFill, Alignment
            
            # Header formatting
            header_font = Font(bold=True, color="FFFFFF")
            header_fill = PatternFill(start_color="366092", end_color="366092", fill_type="solid")
            
            for cell in worksheet[1]:
                cell.font = header_font
                cell.fill = header_fill
                cell.alignment = Alignment(horizontal="center")
            
            # Auto-adjust column widths
            for column in worksheet.columns:
                max_length = 0
                column_letter = column[0].column_letter
                for cell in column:
                    try:
                        if len(str(cell.value)) > max_length:
                            max_length = len(str(cell.value))
                    except:
                        pass
                adjusted_width = min(max_length + 2, 80)  # Increased width for descriptions
                worksheet.column_dimensions[column_letter].width = adjusted_width
            
            # Set text wrapping for description column
            from openpyxl.styles import Alignment
            for row in worksheet.iter_rows(min_row=2):
                for cell in row:
                    if cell.column == 3:  # Test_Description column
                        cell.alignment = Alignment(wrap_text=True, vertical='top')
        
        output.seek(0)
        return output.getvalue()
    
    def parse_excel_file(self, uploaded_file) -> List[Dict]:
        """Parse uploaded Excel file and convert to test scenarios"""
        try:
            # Read Excel file
            df = pd.read_excel(uploaded_file, sheet_name='Test_Scenarios')
            
            # Validate required columns
            required_columns = ['Test_ID', 'Test_Name', 'Test_Description']
            missing_columns = [col for col in required_columns if col not in df.columns]
            
            if missing_columns:
                raise ValueError(f"Missing required columns: {missing_columns}")
            
            # Check for optional credential columns
            has_credentials = 'Username' in df.columns and 'Password' in df.columns
            
            # Convert to test scenarios
            test_scenarios = []
            for index, row in df.iterrows():
                # Skip empty rows - check if Test_ID and Test_Description are empty or NaN
                test_id = str(row.get('Test_ID', '')).strip()
                test_description = str(row.get('Test_Description', '')).strip()
                
                if not test_id or test_id == 'nan' or not test_description or test_description == 'nan':
                    continue
                
                test_scenario = {
                    "test_id": test_id,
                    "test_name": str(row.get('Test_Name', '')).strip() if not pd.isna(row.get('Test_Name')) and str(row.get('Test_Name', '')).strip() != 'nan' else f"Test {index + 1}",
                    "test_description": test_description,
                    "expected_result": str(row.get('Expected_Result', '')).strip() if not pd.isna(row.get('Expected_Result')) and str(row.get('Expected_Result', '')).strip() != 'nan' else '',
                    "priority": str(row.get('Priority', 'Medium')).strip() if not pd.isna(row.get('Priority')) and str(row.get('Priority', '')).strip() != 'nan' else 'Medium'
                }
                
                # Add credentials if available
                if has_credentials:
                    username = str(row.get('Username', '')).strip() if not pd.isna(row.get('Username')) and str(row.get('Username', '')).strip() != 'nan' else ''
                    password = str(row.get('Password', '')).strip() if not pd.isna(row.get('Password')) and str(row.get('Password', '')).strip() != 'nan' else ''
                    test_scenario["username"] = username
                    test_scenario["password"] = password
                else:
                    test_scenario["username"] = ""
                    test_scenario["password"] = ""
                
                test_scenarios.append(test_scenario)
            
            return test_scenarios
            
        except Exception as e:
            raise ValueError(f"Error parsing Excel file: {str(e)}")
    
    def get_template_info(self) -> Dict:
        """Get information about the template"""
        return {
            "name": "Test Scenarios Template",
            "description": "Write test cases in natural language - no technical knowledge required!",
            "features": [
                "✅ No XPath or CSS selectors needed",
                "✅ Write test steps in plain English", 
                "✅ AI automatically finds elements",
                "✅ Step-by-step test descriptions",
                "✅ Priority and expected results included"
            ],
            "use_case": "Perfect for beginners and non-technical users who want to create comprehensive test scenarios"
        }

# -------------------------------
# Test Case Classes
# -------------------------------
@dataclass
class TestCase:
    """Represents a single test case"""
    action: str
    target: str
    value: str = ""
    description: str = ""
    test_id: str = ""
    priority: str = "Medium"
    category: str = "General"
    expected_result: str = ""
    username: str = ""
    password: str = ""

class TestSuite:
    """Represents a collection of test cases"""
    
    def __init__(self, name: str):
        self.name = name
        self.test_cases: List[TestCase] = []
    
    def add_test(self, test_case: TestCase):
        """Add a test case to the suite"""
        self.test_cases.append(test_case)
    
    def get_tests(self) -> List[TestCase]:
        """Get all test cases in the suite"""
        return self.test_cases

# -------------------------------
# Universal Test Executor
# -------------------------------
class UniversalTester:
    """Executes tests on ANY web application with AI intelligence"""
    
    def __init__(self, driver, use_ai=True, provided_username=None, provided_password=None):
        self.driver = driver
        self.discoverer = UniversalDiscoverer(driver)
        self.test_results = []
        self.pages_tested = set()
        self.use_ai = use_ai
        self.ai_helper = AIHelper() if use_ai else None
        self.provided_username = provided_username
        self.provided_password = provided_password
    
    def test_current_page(self) -> List[str]:
        """Test all elements on current page with intelligent detection"""
        logs = []
        current_url = self.driver.current_url
        
        logs.append(f"📍 Testing page: {current_url}")
        self.pages_tested.add(current_url)
        
        # Discover all elements
        logs.append("🔍 Discovering elements...")
        elements = self.discoverer.discover_all_elements()
        summary = self.discoverer.get_summary()
        
        logs.append(f"✅ Found {summary['total']} interactive elements:")
        logs.append(f"  • Buttons: {summary['buttons']}")
        logs.append(f"  • Input fields: {summary['inputs']}")
        logs.append(f"  • Dropdowns: {summary['dropdowns']}")
        logs.append(f"  • Links: {summary['links']}")
        logs.append(f"  • Other clickables: {summary['clickables']}")
        
        # Get page content for AI analysis
        try:
            page_content = self.driver.find_element(By.TAG_NAME, "body").text[:1000]
        except:
            page_content = ""
        
        # AI analysis and page type detection
        page_type_info = None
        if self.use_ai and self.ai_helper:
            logs.append("🤖 AI analyzing page type...")
            page_type_info = self.ai_helper.detect_page_type(page_content, summary)
            logs.append(f"  🤖 AI detected page type: {page_type_info.get('page_type', 'unknown')}")
            logs.append(f"  🤖 Testing approach: {page_type_info.get('testing_approach', 'default')}")
            logs.append(f"  🤖 Confidence: {page_type_info.get('confidence', 'low')}")
        else:
            # Fallback detection
            page_type_info = {
                "page_type": "other",
                "testing_approach": "generate_test_data",
                "confidence": "low"
            }
        
        # Apply intelligent testing based on page type
        logs.extend(self._apply_intelligent_testing(elements, page_type_info, summary))
        
        return logs
    
    def _apply_intelligent_testing(self, elements: Dict, page_type_info: Dict, summary: Dict) -> List[str]:
        """Apply intelligent testing based on detected page type"""
        logs = []
        page_type = page_type_info.get("page_type", "other")
        testing_approach = page_type_info.get("testing_approach", "generate_test_data")
        
        logs.append(f"🎯 Applying {testing_approach} strategy for {page_type} page")
        
        if page_type == "login" and testing_approach == "use_provided_credentials":
            logs.extend(self._handle_login_page(elements))
        elif page_type == "chat" and testing_approach == "chat_interaction":
            logs.extend(self._handle_chat_interface(elements))
        else:
            # Default testing approach
            logs.extend(self._handle_general_testing(elements, page_type_info))
        
        return logs
    
    def _handle_login_page(self, elements: Dict) -> List[str]:
        """Handle login page with provided credentials"""
        logs = []
        
        # Determine credential strategy
        if self.use_ai and self.ai_helper:
            credential_strategy = self.ai_helper.determine_credential_strategy(
                "login", self.provided_username, self.provided_password
            )
            logs.append(f"🔐 {credential_strategy['reason']}")
            
            if credential_strategy["use_provided"]:
                username = credential_strategy["username"]
                password = credential_strategy["password"]
                logs.append(f"📧 Using provided username: {username}")
                logs.append("🔑 Using provided password: ***hidden***")
            else:
                username = "testuser"
                password = "Test123!"
                logs.append("⚠️ No credentials provided, using default test credentials")
        else:
            username = self.provided_username or "testuser"
            password = self.provided_password or "Test123!"
            if self.provided_username and self.provided_password:
                logs.append("📧 Using provided credentials for login")
            else:
                logs.append("⚠️ Using default test credentials")
        
        # Fill login fields
        if elements["inputs"]:
            logs.append("📝 Filling login form...")
            username_filled = False
            password_filled = False
            
            for inp in elements["inputs"]:
                try:
                    input_type = inp["type"]
                    input_name = inp.get("name", "").lower()
                    input_placeholder = inp.get("placeholder", "").lower()
                    
                    # Identify username/email field
                    if (input_type in ["email", "text"] and 
                        any(keyword in input_name + input_placeholder for keyword in 
                            ["user", "email", "login", "username"])):
                        
                        inp["element"].clear()
                        inp["element"].send_keys(username)
                        logs.append(f"  ✅ Filled username field: {username}")
                        username_filled = True
                        
                    # Identify password field
                    elif input_type == "password":
                        inp["element"].clear()
                        inp["element"].send_keys(password)
                        logs.append("  ✅ Filled password field: ***hidden***")
                        password_filled = True
                        
                except Exception as e:
                    logs.append(f"  ⚠️ Could not fill {inp['type']} field: {str(e)[:50]}")
            
            if not username_filled:
                logs.append("⚠️ Could not find username field")
            if not password_filled:
                logs.append("⚠️ Could not find password field")
        
        # Try to submit login form
        if elements["buttons"]:
            logs.append("🔘 Looking for submit button...")
            for btn in elements["buttons"]:
                button_text = btn['text'].lower()
                if any(keyword in button_text for keyword in ["login", "sign", "submit", "enter"]):
                    try:
                        btn["element"].click()
                        logs.append(f"  ✅ Clicked login button: {btn['text']}")
                        time.sleep(2)  # Wait for login to process
                        break
                    except Exception as e:
                        logs.append(f"  ⚠️ Could not click button: {str(e)[:50]}")
        
        return logs
    
    def _handle_chat_interface(self, elements: Dict) -> List[str]:
        """Handle chat interface with intelligent message generation"""
        logs = []
        
        logs.append("💬 Detected chat interface - generating contextual test messages")
        
        # Get page content for better message generation
        try:
            page_content = self.driver.find_element(By.TAG_NAME, "body").text[:1000]
        except:
            page_content = ""
        
        if self.use_ai and self.ai_helper:
            # Generate multiple intelligent chat messages for comprehensive testing
            chat_messages = self.ai_helper.generate_multiple_chat_messages(page_content=page_content, count=2)
            logs.append(f"🤖 AI generated {len(chat_messages)} contextual messages for testing")
        else:
            chat_messages = [self.ai_helper._get_contextual_chat_message(page_content) if self.ai_helper else "Hello, this is a test message"]
            logs.append(f"📝 Using contextual message: {chat_messages[0]}")
        
        # Find and fill message input with enhanced detection
        if elements["inputs"]:
            message_input = None
            message_input_confidence = 0
            
            for inp in elements["inputs"]:
                input_type = inp["type"]
                input_name = inp.get("name", "").lower()
                input_placeholder = inp.get("placeholder", "").lower()
                input_id = inp.get("element").get_attribute("id") or ""
                input_class = inp.get("element").get_attribute("class") or ""
                
                # Enhanced message input detection with confidence scoring
                confidence = 0
                
                # High confidence indicators
                if any(keyword in input_name + input_placeholder + input_id + input_class for keyword in 
                       ["message", "chat", "prompt", "input", "text", "query", "question"]):
                    confidence += 3
                
                # Medium confidence indicators
                if any(keyword in input_name + input_placeholder + input_id + input_class for keyword in 
                       ["reply", "send", "type", "write", "ask"]):
                    confidence += 2
                
                # Look for common chat interface patterns
                if "textarea" in input_type or input_type == "text":
                    confidence += 1
                
                # Prefer larger input areas (likely main chat input)
                try:
                    size = inp["element"].size
                    if size["height"] > 30 or size["width"] > 200:
                        confidence += 1
                except:
                    pass
                
                if confidence > message_input_confidence:
                    message_input = inp
                    message_input_confidence = confidence
            
            if message_input and message_input_confidence > 0:
                try:
                    # Send multiple test messages for comprehensive testing
                    for i, chat_message in enumerate(chat_messages, 1):
                        logs.append(f"  📤 Sending test message {i}/{len(chat_messages)}: {chat_message[:50]}...")
                        
                        message_input["element"].clear()
                        message_input["element"].send_keys(chat_message)
                        logs.append(f"    ✅ Filled message field (confidence: {message_input_confidence})")
                        
                        # Enhanced send button detection
                        send_success = False
                        if elements["buttons"]:
                            for btn in elements["buttons"]:
                                button_text = btn['text'].lower()
                                button_class = btn.get("element").get_attribute("class") or ""
                                button_id = btn.get("element").get_attribute("id") or ""
                                
                                # Look for send button with multiple indicators
                                send_indicators = ["send", "submit", "post", "reply", "enter", "go", "ask"]
                                if any(keyword in button_text + button_class + button_id for keyword in send_indicators):
                                    try:
                                        btn["element"].click()
                                        logs.append(f"    ✅ Sent via button: {btn['text']}")
                                        time.sleep(2)  # Wait for message to be processed
                                        send_success = True
                                        break
                                    except Exception as e:
                                        logs.append(f"    ⚠️ Could not click send button: {str(e)[:30]}")
                                        continue
                        
                        # Try pressing Enter as fallback
                        if not send_success:
                            try:
                                message_input["element"].send_keys(Keys.RETURN)
                                logs.append("    ✅ Sent via Enter key")
                                time.sleep(2)
                                send_success = True
                            except Exception as e:
                                logs.append(f"    ⚠️ Could not send via Enter: {str(e)[:30]}")
                        
                        # Wait for potential response and verify
                        if send_success:
                            logs.append("    ⏳ Waiting for response...")
                            time.sleep(3)
                            
                            # Try to detect if a response was generated
                            try:
                                page_content_after = self.driver.find_element(By.TAG_NAME, "body").text
                                if len(page_content_after) > len(page_content) + 50:  # Significant content change
                                    logs.append("    ✅ Detected response - chat is working!")
                                    page_content = page_content_after  # Update for next iteration
                                else:
                                    logs.append("    ℹ️ No immediate response detected")
                            except:
                                logs.append("    ℹ️ Could not verify response")
                        
                        # Small delay between messages
                        if i < len(chat_messages):
                            time.sleep(1)
                    
                    logs.append(f"  🎯 Completed testing with {len(chat_messages)} messages")
                        
                except Exception as e:
                    logs.append(f"  ⚠️ Could not send chat messages: {str(e)[:50]}")
            else:
                logs.append(f"⚠️ Could not find suitable message input field (tried {len(elements['inputs'])} inputs)")
        else:
            logs.append("⚠️ No input fields found for chat interface")
        
        return logs
    
    def _handle_general_testing(self, elements: Dict, page_type_info: Dict) -> List[str]:
        """Handle general testing for non-login, non-chat pages"""
        logs = []
        
        logs.append("🔧 Applying general testing approach")
        
        # Test input fields with appropriate data
        if elements["inputs"]:
            logs.append("📝 Testing input fields...")
            for inp in elements["inputs"][:5]:  # Test up to 5 inputs
                try:
                    # Determine what value to use
                    if self.use_ai and self.ai_helper:
                        # Use AI to generate smart test data
                        test_value = self.ai_helper.generate_test_value_ai(
                            inp["type"],
                            inp.get("placeholder", ""),
                            inp.get("name", "")
                        )
                        logs.append(f"  🤖 AI generated: {test_value}")
                    else:
                        # Use default test data
                        test_value = self._generate_test_value(inp["type"])
                    
                    inp["element"].clear()
                    inp["element"].send_keys(test_value)
                    logs.append(f"  ✅ Filled {inp['type']} field: {inp['placeholder'] or inp['name'] or 'unnamed'}")
                    time.sleep(0.5)
                except Exception as e:
                    logs.append(f"  ⚠️ Could not fill {inp['type']} field")
        
        # Test dropdowns
        if elements["dropdowns"]:
            logs.append("📋 Testing dropdowns...")
            for dropdown in elements["dropdowns"][:3]:  # Test up to 3 dropdowns
                try:
                    from selenium.webdriver.support.select import Select
                    select = Select(dropdown["element"])
                    options = select.options
                    if len(options) > 1:
                        select.select_by_index(1)  # Select second option
                        logs.append(f"  ✅ Selected option in dropdown: {dropdown['name']}")
                except:
                    logs.append(f"  ⚠️ Could not interact with dropdown")
        
        # Test buttons (carefully with AI guidance)
        if elements["buttons"]:
            logs.append("🔘 Found buttons:")
            for btn in elements["buttons"][:10]:  # List first 10 buttons
                button_text = btn['text']
                
                if self.use_ai and self.ai_helper:
                    # Ask AI if button is safe to click
                    is_safe = self.ai_helper.should_click_button(button_text)
                    if not is_safe:
                        logs.append(f"  • {button_text} (🤖 AI: skip - potentially destructive)")
                    else:
                        logs.append(f"  • {button_text} (🤖 AI: safe to test)")
                else:
                    logs.append(f"  • {button_text}")
        
        return logs
    
    def _generate_test_value(self, input_type: str) -> str:
        """Generate appropriate test value for input type (fallback when not using AI)"""
        test_values = {
            "email": "<EMAIL>",
            "password": "Test123!@#",
            "text": "Test input",
            "number": "123",
            "tel": "555-0123",
            "url": "https://example.com",
            "search": "test search",
            "date": "2024-01-01",
            "time": "12:00",
            "color": "#000000"
        }
        return test_values.get(input_type, "test")
    
    def explore_navigation(self, max_depth: int = 2) -> List[str]:
        """Explore navigation by following links"""
        logs = []
        logs.append("🧭 Exploring navigation...")
        
        initial_url = self.driver.current_url
        visited_urls = {initial_url}
        to_visit = []
        
        # Get links from current page
        elements = self.discoverer.discover_all_elements()
        for link in elements["links"][:5]:  # Limit to 5 links
            href = link["href"]
            if href not in visited_urls and self._is_same_domain(href, initial_url):
                to_visit.append((href, link["text"]))
        
        # Visit each link
        for href, text in to_visit[:max_depth]:
            try:
                logs.append(f"  → Navigating to: {text}")
                self.driver.get(href)
                time.sleep(2)
                visited_urls.add(href)
                
                # Test this page
                page_logs = self.test_current_page()
                logs.extend(page_logs)
                
            except Exception as e:
                logs.append(f"  ⚠️ Could not navigate to {text}")
        
        # Return to initial page
        self.driver.get(initial_url)
        logs.append(f"↩️ Returned to initial page")
        
        return logs
    
    def _is_same_domain(self, url1: str, url2: str) -> bool:
        """Check if two URLs are from same domain"""
        from urllib.parse import urlparse
        domain1 = urlparse(url1).netloc
        domain2 = urlparse(url2).netloc
        return domain1 == domain2

# -------------------------------
# Test Case Executor
# -------------------------------
def run_test_cases(url: str, test_suite: TestSuite, username: str = None, password: str = None, show_browser: bool = False, use_ai: bool = True) -> Dict:
    """
    Execute custom test cases on a web application
    
    Args:
        url: The web application URL to test
        test_suite: TestSuite containing test cases to execute
        username: Login username/email (if application requires login)
        password: Login password (if application requires login)
        show_browser: Whether to show the browser window
    """
    
    results = {
        "success": False,
        "url": url,
        "logs": [],
        "test_results": [],
        "summary": {
            "total": 0,
            "passed": 0,
            "failed": 0,
            "errors": 0
        },
        "login_status": "No login attempted"
    }
    
    # Setup Chrome
    chrome_options = Options()
    
    if not show_browser:
        chrome_options.add_argument("--headless=new")
        results["logs"].append("🔄 Running in headless mode (no browser window)")
    else:
        results["logs"].append("👁️ Running with visible browser window")
    
    chrome_options.add_argument("--disable-gpu")
    chrome_options.add_argument("--no-sandbox")
    chrome_options.add_argument("--window-size=1920,1080")
    
    if show_browser:
        chrome_options.add_argument("--start-maximized")
    
    driver = None
    
    try:
        # Launch browser
        driver = webdriver.Chrome(options=chrome_options)
        results["logs"].append("🚀 Browser launched")
        
        # Navigate to URL
        driver.get(url)
        time.sleep(3)
        results["logs"].append(f"📍 Navigated to: {url}")
        
        # Handle login if credentials provided
        if username and password:
            results["logs"].append("="*30)
            results["logs"].append("🔐 LOGIN PROCESS")
            results["logs"].append("="*30)
            
            login_success, login_message = handle_login(driver, username, password)
            
            if login_success:
                results["logs"].append(f"✅ {login_message}")
                results["login_status"] = "Login successful"
                time.sleep(2)
            else:
                results["logs"].append(f"ℹ️ {login_message}")
                results["login_status"] = login_message
            
            results["logs"].append("="*30)
        else:
            results["logs"].append("ℹ️ No login credentials provided - testing as anonymous user")
            results["login_status"] = "No credentials provided"
        
        # Execute test cases
        results["logs"].append("="*50)
        results["logs"].append("🧪 EXECUTING CUSTOM TEST CASES")
        results["logs"].append("="*50)
        
        test_cases = test_suite.get_tests()
        results["summary"]["total"] = len(test_cases)
        
        for i, test_case in enumerate(test_cases, 1):
            results["logs"].append(f"📋 Test {i}/{len(test_cases)}: {test_case.description}")
            
            # Check if test case has specific credentials
            test_username = getattr(test_case, 'username', None) or username
            test_password = getattr(test_case, 'password', None) or password
            
            if hasattr(test_case, 'username') and test_case.username:
                results["logs"].append(f"🔐 Using test-specific credentials: {test_case.username}")
            
            try:
                # Execute the test case with intelligent detection
                success, message = execute_test_case_with_ai(driver, test_case, test_username, test_password, use_ai)
                
                if success:
                    results["test_results"].append({
                        "test_id": test_case.test_id or f"TC{i:03d}",
                        "description": test_case.description,
                        "status": "PASS",
                        "message": message,
                        "action": test_case.action,
                        "target": test_case.target,
                        "priority": test_case.priority,
                        "category": test_case.category
                    })
                    results["summary"]["passed"] += 1
                    results["logs"].append(f"  ✅ PASS: {message}")
                else:
                    results["test_results"].append({
                        "test_id": test_case.test_id or f"TC{i:03d}",
                        "description": test_case.description,
                        "status": "FAIL",
                        "message": message,
                        "action": test_case.action,
                        "target": test_case.target,
                        "priority": test_case.priority,
                        "category": test_case.category
                    })
                    results["summary"]["failed"] += 1
                    results["logs"].append(f"  ❌ FAIL: {message}")
                
            except Exception as e:
                results["test_results"].append({
                    "test_id": test_case.test_id or f"TC{i:03d}",
                    "description": test_case.description,
                    "status": "ERROR",
                    "message": f"Error executing test: {str(e)}",
                    "action": test_case.action,
                    "target": test_case.target,
                    "priority": test_case.priority,
                    "category": test_case.category
                })
                results["summary"]["errors"] += 1
                results["logs"].append(f"  ⚠️ ERROR: {str(e)}")
            
            time.sleep(1)  # Small delay between tests
        
        results["logs"].append("="*50)
        results["logs"].append("✅ TEST EXECUTION COMPLETE")
        results["logs"].append(f"📊 Total: {results['summary']['total']}")
        results["logs"].append(f"✅ Passed: {results['summary']['passed']}")
        results["logs"].append(f"❌ Failed: {results['summary']['failed']}")
        results["logs"].append(f"⚠️ Errors: {results['summary']['errors']}")
        results["logs"].append("="*50)
        
        results["success"] = True
        
    except Exception as e:
        results["logs"].append(f"❌ Error: {str(e)}")
    
    finally:
        if driver:
            driver.quit()
    
    return results

def execute_test_case_with_ai(driver, test_case: TestCase, username: str = None, password: str = None, use_ai: bool = True) -> tuple[bool, str]:
    """Execute a single test case with AI-powered intelligent detection"""
    
    try:
        action = test_case.action.lower()
        target = test_case.target
        value = test_case.value
        
        # Handle natural language scenarios with AI intelligence
        if action == "scenario":
            return execute_natural_language_scenario_with_ai(driver, target, value, username, password, use_ai)
        
        # Handle other actions with standard execution
        return execute_test_case(driver, test_case)
        
    except Exception as e:
        return False, f"Error: {str(e)}"

def execute_natural_language_scenario_with_ai(driver, scenario_description: str, expected_result: str, username: str = None, password: str = None, use_ai: bool = True) -> tuple[bool, str]:
    """Execute a natural language test scenario using AI with intelligent credential handling"""
    
    try:
        # Initialize AI helper if available
        ai_helper = AIHelper() if use_ai and model_loaded else None
        
        # Split the scenario into individual steps
        steps = [step.strip() for step in scenario_description.split('\n') if step.strip()]
        
        execution_log = []
        execution_log.append(f"🤖 Executing scenario with {len(steps)} steps")
        
        # Get page content for AI analysis
        try:
            page_content = driver.find_element(By.TAG_NAME, "body").text[:1000]
        except:
            page_content = ""
        
        # AI page type detection
        page_type_info = None
        if ai_helper:
            # Get elements summary for AI analysis
            discoverer = UniversalDiscoverer(driver)
            elements = discoverer.discover_all_elements()
            summary = discoverer.get_summary()
            
            page_type_info = ai_helper.detect_page_type(page_content, summary)
            execution_log.append(f"🤖 AI detected page type: {page_type_info.get('page_type', 'unknown')}")
            
            # Determine credential strategy
            credential_strategy = ai_helper.determine_credential_strategy(
                page_type_info.get('page_type', 'other'), username, password
            )
            execution_log.append(f"🔐 {credential_strategy['reason']}")
        
        for i, step in enumerate(steps, 1):
            execution_log.append(f"Step {i}: {step}")
            
            # Use AI to interpret and execute the step
            success, message = execute_natural_language_step_with_ai(driver, step, page_type_info, credential_strategy if ai_helper else None)
            
            if success:
                execution_log.append(f"  ✅ {message}")
            else:
                execution_log.append(f"  ❌ {message}")
                return False, f"Failed at step {i}: {message}"
            
            time.sleep(1)  # Small delay between steps
        
        # Verify expected result
        if expected_result:
            page_text = driver.find_element(By.TAG_NAME, "body").text
            if expected_result.lower() in page_text.lower():
                execution_log.append(f"✅ Expected result verified: {expected_result}")
                return True, f"Scenario completed successfully. Expected result: {expected_result}"
            else:
                execution_log.append(f"⚠️ Expected result not found: {expected_result}")
                return False, f"Scenario completed but expected result not found: {expected_result}"
        
        return True, "Scenario completed successfully"
        
    except Exception as e:
        return False, f"Error executing scenario: {str(e)}"

def execute_natural_language_step_with_ai(driver, step: str, page_type_info: Dict = None, credential_strategy: Dict = None) -> tuple[bool, str]:
    """Execute a single natural language step using AI interpretation and intelligent credential handling"""
    
    try:
        step_lower = step.lower()
        
        # Navigate to page
        if "navigate to" in step_lower or "go to" in step_lower:
            # Extract URL or page name
            if "http" in step:
                # Direct URL
                url_match = re.search(r'https?://[^\s]+', step)
                if url_match:
                    url = url_match.group(0)
                    driver.get(url)
                    time.sleep(2)
                    return True, f"Navigated to {url}"
            else:
                # Page name - try to find link or navigate
                page_name = step.split("to")[-1].strip().strip("'\"")
                try:
                    # Try to find a link with this text
                    link = driver.find_element(By.PARTIAL_LINK_TEXT, page_name)
                    link.click()
                    time.sleep(2)
                    return True, f"Navigated to {page_name} page"
                except:
                    return False, f"Could not navigate to {page_name}"
        
        # Fill/Enter text with intelligent credential handling
        elif "enter" in step_lower or "fill" in step_lower or "type" in step_lower:
            # Extract field name and value
            if "'" in step or '"' in step:
                # Extract text in quotes
                text_match = re.search(r"['\"]([^'\"]+)['\"]", step)
                if text_match:
                    text_to_enter = text_match.group(1)
                    
                    # Extract field name
                    field_name = step.split("in")[-1].split("field")[0].strip().strip("'\"")
                    
                    # Check if this is a credential field and we have a strategy
                    if credential_strategy and page_type_info:
                        page_type = page_type_info.get('page_type', 'other')
                        
                        # Handle login credentials intelligently
                        if (page_type == "login" and 
                            any(keyword in field_name.lower() for keyword in ["user", "email", "login"]) and
                            credential_strategy.get("use_provided") and credential_strategy.get("username")):
                            text_to_enter = credential_strategy["username"]
                            return True, f"Used provided username: {text_to_enter}"
                        elif (page_type == "login" and 
                              "password" in field_name.lower() and
                              credential_strategy.get("use_provided") and credential_strategy.get("password")):
                            text_to_enter = credential_strategy["password"]
                            return True, f"Used provided password: ***hidden***"
                    
                    # Find the input field
                    element = find_element_by_description(driver, field_name)
                    if element:
                        element.clear()
                        element.send_keys(text_to_enter)
                        time.sleep(0.5)
                        return True, f"Entered '{text_to_enter}' in {field_name} field"
                    else:
                        return False, f"Could not find field: {field_name}"
            
            return False, f"Could not parse text to enter from: {step}"
        
        # For other actions, use the standard step execution
        else:
            return execute_natural_language_step(driver, step)
    
    except Exception as e:
        return False, f"Error executing step: {str(e)}"

def execute_test_case(driver, test_case: TestCase) -> tuple[bool, str]:
    """Execute a single test case and return success status and message"""
    
    try:
        action = test_case.action.lower()
        target = test_case.target
        value = test_case.value
        
        # Handle natural language scenarios
        if action == "scenario":
            return execute_natural_language_scenario(driver, target, value)
        
        elif action == "navigate":
            driver.get(target)
            time.sleep(2)
            return True, f"Navigated to {target}"
        
        elif action == "click":
            element = find_element(driver, target)
            element.click()
            time.sleep(1)
            return True, f"Clicked element: {target}"
        
        elif action == "fill":
            element = find_element(driver, target)
            element.clear()
            element.send_keys(value)
            time.sleep(0.5)
            return True, f"Filled '{target}' with '{value}'"
        
        elif action == "verify":
            if target.lower() == "page":
                page_text = driver.find_element(By.TAG_NAME, "body").text
                if value.lower() in page_text.lower():
                    return True, f"Found text '{value}' on page"
                else:
                    return False, f"Text '{value}' not found on page"
            else:
                element = find_element(driver, target)
                element_text = element.text
                if value.lower() in element_text.lower():
                    return True, f"Found text '{value}' in element '{target}'"
                else:
                    return False, f"Text '{value}' not found in element '{target}'"
        
        elif action == "wait":
            wait_time = int(value) if value.isdigit() else 2
            time.sleep(wait_time)
            return True, f"Waited for {wait_time} seconds"
        
        elif action == "select":
            from selenium.webdriver.support.select import Select
            element = find_element(driver, target)
            select = Select(element)
            select.select_by_visible_text(value)
            time.sleep(0.5)
            return True, f"Selected '{value}' from dropdown '{target}'"
        
        elif action == "submit":
            element = find_element(driver, target)
            element.submit()
            time.sleep(2)
            return True, f"Submitted form: {target}"
        
        else:
            return False, f"Unknown action: {action}"
    
    except Exception as e:
        return False, f"Error: {str(e)}"

def execute_natural_language_scenario(driver, scenario_description: str, expected_result: str) -> tuple[bool, str]:
    """Execute a natural language test scenario using AI"""
    
    try:
        # Split the scenario into individual steps
        steps = [step.strip() for step in scenario_description.split('\n') if step.strip()]
        
        execution_log = []
        execution_log.append(f"🤖 Executing scenario with {len(steps)} steps")
        
        for i, step in enumerate(steps, 1):
            execution_log.append(f"Step {i}: {step}")
            
            # Use AI to interpret and execute the step
            success, message = execute_natural_language_step(driver, step)
            
            if success:
                execution_log.append(f"  ✅ {message}")
            else:
                execution_log.append(f"  ❌ {message}")
                return False, f"Failed at step {i}: {message}"
            
            time.sleep(1)  # Small delay between steps
        
        # Verify expected result
        if expected_result:
            page_text = driver.find_element(By.TAG_NAME, "body").text
            if expected_result.lower() in page_text.lower():
                execution_log.append(f"✅ Expected result verified: {expected_result}")
                return True, f"Scenario completed successfully. Expected result: {expected_result}"
            else:
                execution_log.append(f"⚠️ Expected result not found: {expected_result}")
                return False, f"Scenario completed but expected result not found: {expected_result}"
        
        return True, "Scenario completed successfully"
        
    except Exception as e:
        return False, f"Error executing scenario: {str(e)}"

def execute_natural_language_step(driver, step: str) -> tuple[bool, str]:
    """Execute a single natural language step using AI interpretation"""
    
    try:
        step_lower = step.lower()
        
        # Navigate to page
        if "navigate to" in step_lower or "go to" in step_lower:
            # Extract URL or page name
            if "http" in step:
                # Direct URL
                url_match = re.search(r'https?://[^\s]+', step)
                if url_match:
                    url = url_match.group(0)
                    driver.get(url)
                    time.sleep(2)
                    return True, f"Navigated to {url}"
            else:
                # Page name - try to find link or navigate
                page_name = step.split("to")[-1].strip().strip("'\"")
                try:
                    # Try to find a link with this text
                    link = driver.find_element(By.PARTIAL_LINK_TEXT, page_name)
                    link.click()
                    time.sleep(2)
                    return True, f"Navigated to {page_name} page"
                except:
                    return False, f"Could not navigate to {page_name}"
        
        # Click actions
        elif "click" in step_lower:
            # Extract what to click
            click_target = step.split("click")[-1].strip().strip("on").strip().strip("'\"")
            
            # Try different strategies to find the element
            element = find_element_by_description(driver, click_target)
            if element:
                element.click()
                time.sleep(1)
                return True, f"Clicked {click_target}"
            else:
                return False, f"Could not find element to click: {click_target}"
        
        # Fill/Enter text
        elif "enter" in step_lower or "fill" in step_lower or "type" in step_lower:
            # Extract field name and value
            if "'" in step or '"' in step:
                # Extract text in quotes
                text_match = re.search(r"['\"]([^'\"]+)['\"]", step)
                if text_match:
                    text_to_enter = text_match.group(1)
                    
                    # Extract field name
                    field_name = step.split("in")[-1].split("field")[0].strip().strip("'\"")
                    
                    # Find the input field
                    element = find_element_by_description(driver, field_name)
                    if element:
                        element.clear()
                        element.send_keys(text_to_enter)
                        time.sleep(0.5)
                        return True, f"Entered '{text_to_enter}' in {field_name} field"
                    else:
                        return False, f"Could not find field: {field_name}"
            
            return False, f"Could not parse text to enter from: {step}"
        
        # Verify actions
        elif "verify" in step_lower or "check" in step_lower:
            # Extract what to verify
            if "'" in step or '"' in step:
                text_match = re.search(r"['\"]([^'\"]+)['\"]", step)
                if text_match:
                    text_to_verify = text_match.group(1)
                    page_text = driver.find_element(By.TAG_NAME, "body").text
                    if text_to_verify.lower() in page_text.lower():
                        return True, f"Verified text '{text_to_verify}' is present"
                    else:
                        return False, f"Text '{text_to_verify}' not found on page"
            
            return False, f"Could not parse verification text from: {step}"
        
        # Wait actions
        elif "wait" in step_lower:
            # Extract wait time
            time_match = re.search(r'(\d+)\s*seconds?', step)
            if time_match:
                wait_time = int(time_match.group(1))
                time.sleep(wait_time)
                return True, f"Waited for {wait_time} seconds"
            else:
                time.sleep(2)  # Default wait
                return True, "Waited for 2 seconds"
        
        # Press Enter
        elif "press enter" in step_lower:
            from selenium.webdriver.common.keys import Keys
            active_element = driver.switch_to.active_element
            active_element.send_keys(Keys.RETURN)
            time.sleep(1)
            return True, "Pressed Enter"
        
        else:
            return False, f"Could not interpret step: {step}"
    
    except Exception as e:
        return False, f"Error executing step: {str(e)}"

def find_element_by_description(driver, description: str):
    """Find element using natural language description"""
    
    # Try different strategies to find the element
    strategies = [
        # By partial text match
        (By.PARTIAL_LINK_TEXT, description),
        (By.PARTIAL_LINK_TEXT, description.lower()),
        
        # By exact text match
        (By.LINK_TEXT, description),
        
        # By placeholder text
        (By.XPATH, f"//input[@placeholder='{description}']"),
        (By.XPATH, f"//input[contains(@placeholder, '{description}')]"),
        
        # By button text
        (By.XPATH, f"//button[contains(text(), '{description}')]"),
        (By.XPATH, f"//input[@value='{description}']"),
        
        # By common field names
        (By.NAME, description.lower()),
        (By.ID, description.lower()),
        (By.CLASS_NAME, description.lower()),
        
        # By aria-label
        (By.XPATH, f"//*[@aria-label='{description}']"),
        (By.XPATH, f"//*[contains(@aria-label, '{description}')]"),
        
        # By title attribute
        (By.XPATH, f"//*[@title='{description}']"),
        (By.XPATH, f"//*[contains(@title, '{description}')]"),
    ]
    
    for by, value in strategies:
        try:
            element = driver.find_element(by, value)
            if element.is_displayed() and element.is_enabled():
                return element
        except:
            continue
    
    return None

def find_element(driver, target: str):
    """Find element using various strategies"""
    
    # Try different strategies to find the element
    strategies = [
        (By.ID, target),
        (By.XPATH, target),
        (By.CSS_SELECTOR, target),
        (By.LINK_TEXT, target),
        (By.PARTIAL_LINK_TEXT, target),
        (By.NAME, target),
        (By.CLASS_NAME, target)
    ]
    
    for by, value in strategies:
        try:
            element = driver.find_element(by, value)
            if element.is_displayed() and element.is_enabled():
                return element
        except:
            continue
    
    # If all strategies fail, try to find by text content
    try:
        element = driver.find_element(By.XPATH, f"//*[contains(text(), '{target}')]")
        if element.is_displayed() and element.is_enabled():
            return element
    except:
        pass
    
    raise NoSuchElementException(f"Could not find element: {target}")

# -------------------------------
# Login Handler
# -------------------------------
def handle_login(driver, username: str, password: str) -> tuple[bool, str]:
    """
    Automatically handle login forms
    Returns: (success, message)
    """
    
    # Common patterns for login detection
    login_indicators = [
        "//input[@type='password']",
        "//input[@type='email']",
        "//button[contains(text(), 'Sign')]",
        "//button[contains(text(), 'Login')]",
        "//button[contains(text(), 'Log in')]",
        "//a[contains(text(), 'Sign in')]",
        "//a[contains(text(), 'Log in')]"
    ]
    
    # Check if we're on a login page
    is_login_page = False
    for indicator in login_indicators:
        try:
            element = driver.find_element(By.XPATH, indicator)
            if element.is_displayed():
                is_login_page = True
                break
        except:
            continue
    
    if not is_login_page:
        return False, "No login form detected on page"
    
    # Try to fill login form
    try:
        # Find username/email field
        username_selectors = [
            "//input[@type='email']",
            "//input[@type='text'][@name='username']",
            "//input[@type='text'][@name='email']",
            "//input[@type='text'][contains(@placeholder, 'mail')]",
            "//input[@type='text'][contains(@placeholder, 'user')]",
            "//input[@name='login']",
            "//input[@id='username']",
            "//input[@id='email']",
            "//input[@type='text']"  # Fallback to any text input
        ]
        
        username_filled = False
        for selector in username_selectors:
            try:
                field = driver.find_element(By.XPATH, selector)
                if field.is_displayed() and field.is_enabled():
                    field.clear()
                    field.send_keys(username)
                    username_filled = True
                    break
            except:
                continue
        
        if not username_filled:
            return False, "Could not find username/email field"
        
        # Find password field
        password_field = driver.find_element(By.XPATH, "//input[@type='password']")
        password_field.clear()
        password_field.send_keys(password)
        
        # Submit form
        submit_selectors = [
            "//button[@type='submit']",
            "//button[contains(text(), 'Sign')]",
            "//button[contains(text(), 'Login')]",
            "//button[contains(text(), 'Log in')]",
            "//input[@type='submit']",
            "//button[contains(@class, 'login')]",
            "//button[contains(@class, 'signin')]"
        ]
        
        for selector in submit_selectors:
            try:
                button = driver.find_element(By.XPATH, selector)
                if button.is_displayed() and button.is_enabled():
                    button.click()
                    time.sleep(3)
                    
                    # Check if login was successful by looking for password field
                    try:
                        driver.find_element(By.XPATH, "//input[@type='password']")
                        # If password field still exists, login might have failed
                        return False, "Login may have failed - still on login page"
                    except:
                        # Password field gone, likely logged in
                        return True, "Login successful"
            except:
                continue
        
        # Try pressing Enter as fallback
        password_field.send_keys(Keys.RETURN)
        time.sleep(3)
        
        # Check if login was successful
        try:
            driver.find_element(By.XPATH, "//input[@type='password']")
            return False, "Login may have failed - still on login page"
        except:
            return True, "Login successful"
        
    except Exception as e:
        return False, f"Login error: {str(e)[:50]}"

# -------------------------------
# Main Test Runner
# -------------------------------
def run_universal_test(url: str, username: str = None, password: str = None, explore_nav: bool = True, use_ai: bool = True, show_browser: bool = False) -> Dict:
    """
    Run universal test on ANY web application with optional AI intelligence
    
    Args:
        url: The web application URL to test
        username: Login username/email (if application requires login)
        password: Login password (if application requires login)
        explore_nav: Whether to explore multiple pages
        use_ai: Whether to use AI intelligence
        show_browser: Whether to show the browser window
    """
    
    results = {
        "success": False,
        "url": url,
        "logs": [],
        "pages_tested": 0,
        "elements_found": 0,
        "test_summary": {},
        "ai_insights": [],
        "login_status": "No login attempted"
    }
    
    # Setup Chrome
    chrome_options = Options()
    
    # Only add headless if user doesn't want to see the browser
    if not show_browser:
        chrome_options.add_argument("--headless=new")
        results["logs"].append("🔄 Running in headless mode (no browser window)")
    else:
        results["logs"].append("👁️ Running with visible browser window")
    
    chrome_options.add_argument("--disable-gpu")
    chrome_options.add_argument("--no-sandbox")
    chrome_options.add_argument("--window-size=1920,1080")
    
    # If showing browser, start maximized for better visibility
    if show_browser:
        chrome_options.add_argument("--start-maximized")
    
    driver = None
    
    try:
        # Launch browser
        driver = webdriver.Chrome(options=chrome_options)
        results["logs"].append("🚀 Browser launched")
        
        # Navigate to URL
        driver.get(url)
        time.sleep(3)
        results["logs"].append(f"📍 Navigated to: {url}")
        
        # HANDLE LOGIN IF CREDENTIALS PROVIDED
        if username and password:
            results["logs"].append("="*30)
            results["logs"].append("🔐 LOGIN PROCESS")
            results["logs"].append("="*30)
            results["logs"].append(f"📧 Username provided: {username}")
            results["logs"].append("🔑 Password provided: ***hidden***")
            results["logs"].append("🔍 Looking for login form...")
            
            login_success, login_message = handle_login(driver, username, password)
            
            if login_success:
                results["logs"].append(f"✅ {login_message}")
                results["login_status"] = "Login successful"
                time.sleep(2)  # Wait for app to load after login
                results["logs"].append("⏳ Waiting for application to load...")
                time.sleep(2)
            else:
                results["logs"].append(f"ℹ️ {login_message}")
                results["login_status"] = login_message
                
                # If no login form found, it's okay - app might not need login
                if "No login form detected" in login_message:
                    results["logs"].append("✅ Proceeding with testing - app doesn't require login")
                else:
                    # Login failed for some reason
                    results["logs"].append("⚠️ Continuing anyway - app might be accessible")
            
            results["logs"].append("="*30)
        else:
            # No credentials provided
            results["logs"].append("ℹ️ No login credentials provided - testing as anonymous user")
            results["login_status"] = "No credentials provided"
            
            # Check if we're on a login page anyway
            login_indicators = ["//input[@type='password']", "//button[contains(text(), 'Login')]"]
            on_login_page = False
            
            for indicator in login_indicators:
                try:
                    element = driver.find_element(By.XPATH, indicator)
                    if element.is_displayed():
                        on_login_page = True
                        break
                except:
                    continue
            
            if on_login_page:
                results["logs"].append("⚠️ WARNING: This appears to be a login page")
                results["logs"].append("💡 TIP: Provide login credentials in the UI for full testing")
                results["login_status"] = "Login required but no credentials provided"
        
        # Initialize tester with or without AI
        if use_ai:
            results["logs"].append("🤖 AI Intelligence enabled - Qwen model active")
        
        tester = UniversalTester(driver, use_ai=use_ai, provided_username=username, provided_password=password)
        
        # Test current page
        results["logs"].append("="*50)
        results["logs"].append("🧪 STARTING AUTOMATED TESTING")
        if use_ai:
            results["logs"].append("🤖 WITH AI-POWERED INTELLIGENCE")
        results["logs"].append("="*50)
        
        page_logs = tester.test_current_page()
        results["logs"].extend(page_logs)
        
        # Extract AI insights from logs
        if use_ai:
            for log in page_logs:
                if "🤖" in log:
                    results["ai_insights"].append(log)
        
        # Explore navigation if enabled
        if explore_nav:
            nav_logs = tester.explore_navigation(max_depth=3)
            results["logs"].extend(nav_logs)
        
        # Get final summary
        results["pages_tested"] = len(tester.pages_tested)
        final_summary = tester.discoverer.get_summary()
        results["elements_found"] = final_summary["total"]
        results["test_summary"] = final_summary
        
        results["logs"].append("="*50)
        results["logs"].append("✅ TESTING COMPLETE")
        results["logs"].append(f"📊 Pages tested: {results['pages_tested']}")
        results["logs"].append(f"🎯 Total elements found: {results['elements_found']}")
        results["logs"].append(f"🔐 Login status: {results['login_status']}")
        if use_ai and results["ai_insights"]:
            results["logs"].append(f"🤖 AI insights generated: {len(results['ai_insights'])}")
        results["logs"].append("="*50)
        
        results["success"] = True
        
    except Exception as e:
        results["logs"].append(f"❌ Error: {str(e)}")
    
    finally:
        if driver:
            driver.quit()
    
    return results

# -------------------------------
# Streamlit UI
# -------------------------------
st.set_page_config(page_title="Universal Selenium Tester", page_icon="🤖", layout="wide")

st.title("🤖 Universal Selenium Automation Tester")
st.markdown("**Works with ANY web application - Automated or Custom Test Cases!**")

# Show Falcon model status in sidebar
with st.sidebar:
    st.markdown("### 🧠 Qwen AI Model")
    
    if model_loaded:
        st.success("✅ Model Loaded")
        st.caption("AI features available")
        st.info("🤖 **Intelligent Features:**")
        st.write("• Smart page detection")
        st.write("• Auto credential handling")
        st.write("• Enhanced chat interface detection")
        st.write("• Context-aware testing")
        st.write("• Intelligent chat message generation")
        st.write("• Multiple test message support")
    else:
        st.warning("⚠️ Model not loaded")
        st.caption("Running without AI - using default test values")
    
    st.markdown("### 📖 Test Actions")
    st.info("""
    **Available Actions:**
    - **navigate**: Go to URL
    - **click**: Click element
    - **fill**: Fill input field
    - **verify**: Check text/element
    - **wait**: Pause execution
    - **select**: Choose dropdown option
    - **submit**: Submit form
    
    **Target Examples:**
    - `username` (ID)
    - `//button[@type='submit']` (XPath)
    - `.btn-primary` (CSS)
    - `Sign In` (Link text)
    """)

# Choose testing mode
st.markdown("### 🎯 Choose Testing Mode")
testing_mode = st.radio(
    "Select how you want to test:",
    ["🔍 Automated Discovery Testing", "📝 Custom Test Cases", "🔄 Both (Discovery + Custom)"],
    index=0
)

# Main configuration section
st.markdown("### 🔧 Application Configuration")

# URL input
url = st.text_input(
    "Enter Application URL:",
    placeholder="https://any-website.com",
    help="Works with ANY web application - with or without login"
)

# Login section
st.markdown("### 🔐 Login Configuration (Optional)")

col1, col2 = st.columns(2)

with col1:
    needs_login = st.checkbox(
        "Application requires login", 
        value=False,
        help="Check this if your application needs login to access main features"
    )

with col2:
    if needs_login:
        st.success("✅ Login credentials will be used")
    else:
        st.info("ℹ️ Testing without login")

# Login credentials (only show if needed)
if needs_login:
    st.markdown("#### Enter Login Credentials")
    col3, col4 = st.columns(2)
    with col3:
        username = st.text_input(
            "Username/Email:",
            placeholder="<EMAIL>",
            help="Enter the username or email for login"
        )
    with col4:
        password = st.text_input(
            "Password:",
            type="password",
            help="Enter the password for login"
        )
    
    # Validate credentials are provided
    if needs_login and (not username or not password):
        st.warning("⚠️ Please provide both username and password for login")
else:
    username = password = None

# Custom Test Cases Section (Now Excel-based)
if testing_mode in ["📝 Custom Test Cases", "🔄 Both (Discovery + Custom)"]:
    st.markdown("---")
    st.markdown("### 📝 Custom Test Cases")
    st.info("🎯 **Beginner-Friendly Approach**: Download an Excel template, write your test cases in descriptive language, and upload it back to run tests!")
    
    # Initialize Excel handler
    excel_handler = ExcelTemplateHandler()
    
    # Template download section
    st.markdown("#### 📥 Download Test Scenarios Template")
    
    template_info = excel_handler.get_template_info()
    
    col1, col2 = st.columns([1, 2])
    
    with col1:
        if st.button("📋 Download Template", use_container_width=True, type="primary"):
            template_data = excel_handler.generate_template()
            st.download_button(
                label="⬇️ Download Test Scenarios Template",
                data=template_data,
                file_name=f"test_scenarios_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx",
                mime="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
            )
            st.success("✅ Template ready for download!")
    
    with col2:
        st.markdown(f"**{template_info['name']}**")
        st.write(template_info['description'])
        st.markdown("**Key Features:**")
        for feature in template_info['features']:
            st.write(feature)
    
    # Template information
    with st.expander("📚 Template Information & Examples"):
        st.markdown(f"**{template_info['name']}**")
        st.write(f"📝 {template_info['description']}")
        st.write(f"💡 {template_info['use_case']}")
        st.markdown("---")
        
        st.markdown("**📋 Template Columns:**")
        st.markdown("""
        | Column | Description | Example |
        |--------|-------------|---------|
        | **Test_ID** | Unique identifier | TS001, TS002 |
        | **Test_Name** | Brief description | "User Login", "Contact Form" |
        | **Test_Description** | Step-by-step instructions | "1. Navigate to login page\\n2. Enter username..." |
        | **Expected_Result** | What should happen | "User should be logged in successfully" |
        | **Priority** | Importance level | High, Medium, Low |
        | **Username** | Specific username for this test (optional) | "<EMAIL>" |
        | **Password** | Specific password for this test (optional) | "admin123" |
        """)
        
        st.markdown("**🎯 Why Use This Approach?**")
        st.markdown("""
        - ✅ **No technical knowledge required** - Write in plain English
        - ✅ **AI-powered element discovery** - No need to know XPath or CSS selectors
        - ✅ **Natural language descriptions** - Describe what you want to test
        - ✅ **Step-by-step format** - Clear, numbered instructions
        - ✅ **Professional results** - Get detailed test reports
        - ✅ **Easy to share** - Send Excel files to team members
        - ✅ **Intelligent credential handling** - Different credentials per test case
        - ✅ **Smart page detection** - AI automatically detects login, chat, forms
        - ✅ **Context-aware testing** - Adapts behavior based on page type
        """)
        
        st.markdown("**💡 Example Test Description:**")
        st.code("""
1. Navigate to the application homepage
2. Click on the login button
3. Enter 'testuser' in the username field
4. Enter 'Test123!' in the password field
5. Click the submit button
6. Wait for 2 seconds
7. Verify that 'Dashboard' text is displayed
        """, language="text")
        
        st.markdown("**📋 Template Features:**")
        st.markdown("""
        - ✅ **Empty template** - Only column headers, no sample data
        - ✅ **Instructions sheet** - Contains examples and guidance
        - ✅ **Ready to fill** - Start writing your test scenarios immediately
        - ✅ **Professional format** - Properly formatted Excel file
        """)
    
    # File upload section
    st.markdown("#### 📤 Upload Your Test Cases")
    
    uploaded_file = st.file_uploader(
        "Choose an Excel file with your test scenarios",
        type=['xlsx', 'xls'],
        help="Upload the Excel file you filled with test scenarios"
    )
    
    if uploaded_file is not None:
        try:
            # Parse the uploaded file
            test_scenarios = excel_handler.parse_excel_file(uploaded_file)
            
            if test_scenarios:
                st.success(f"✅ Successfully loaded {len(test_scenarios)} test scenarios from Excel file!")
                
                # Display loaded test scenarios
                st.markdown("#### 📋 Loaded Test Scenarios:")
                
                for i, ts in enumerate(test_scenarios, 1):
                    priority_color = {
                        "High": "🔴",
                        "Medium": "🟡", 
                        "Low": "🟢"
                    }.get(ts.get('priority', 'Medium'), '🟡')
                    
                    with st.expander(f"{i}. {priority_color} **{ts['test_name']}** (ID: {ts['test_id']})"):
                        st.markdown(f"**Test ID:** {ts['test_id']}")
                        st.markdown(f"**Priority:** {ts['priority']}")
                        st.markdown(f"**Expected Result:** {ts['expected_result']}")
                        st.markdown("**Test Steps:**")
                        st.text(ts['test_description'])
                
                # Store test scenarios in session state for execution
                st.session_state.test_cases = []
                for ts in test_scenarios:
                    # Convert test scenario to test case format for execution
                    st.session_state.test_cases.append({
                        "action": "scenario",  # Special action type for scenarios
                        "target": ts['test_description'],
                        "value": ts['expected_result'],
                        "description": f"{ts['test_id']}: {ts['test_name']}",
                        "test_id": ts['test_id'],
                        "priority": ts['priority'],
                        "username": ts.get('username', ''),
                        "password": ts.get('password', '')
                    })
                
                # Show test scenario summary
                priorities = {}
                
                for ts in test_scenarios:
                    prio = ts.get('priority', 'Medium')
                    priorities[prio] = priorities.get(prio, 0) + 1
                
                col1, col2 = st.columns(2)
                
                with col1:
                    st.metric("Total Test Scenarios", len(test_scenarios))
                
                with col2:
                    st.markdown("**⚡ Priority Breakdown:**")
                    for prio, count in priorities.items():
                        st.write(f"• {prio}: {count} tests")
                
            else:
                st.warning("⚠️ No valid test scenarios found in the Excel file. Please check the format.")
                st.session_state.test_cases = []
        
        except Exception as e:
            st.error(f"❌ Error reading Excel file: {str(e)}")
            st.info("💡 Make sure your Excel file has the correct format with 'Test_Scenarios' sheet and required columns.")
            st.session_state.test_cases = []
    
    else:
        st.session_state.test_cases = []
        st.info("📁 **Getting Started**: Download a template above, fill it with your test cases, and upload it here!")
        
        # Show quick start guide
        with st.expander("🚀 Quick Start Guide"):
            st.markdown("""
            **Step 1: Download Template**
            - Click the "Download Template" button above
            - Save the Excel file to your computer
            
            **Step 2: Fill Test Scenarios**
            - Open the downloaded Excel file (it will be empty with just column headers)
            - Check the "Instructions" sheet for examples and guidance
            - Write your test scenarios in the "Test_Description" column
            - Use natural language - describe what you want to test step by step
            - No need to know XPath, CSS selectors, or technical details
            
            **Step 3: Upload & Run**
            - Upload your completed Excel file here
            - Click "Start Testing" to run all your test scenarios
            - AI will automatically find elements and execute your tests
            - View detailed results and reports
            
            **💡 Pro Tip**: Check the "Instructions" sheet in the template for examples of how to write test scenarios!
            
            **Example Test Description:**
            ```
            1. Navigate to the login page
            2. Enter 'testuser' in the username field
            3. Enter 'password123' in the password field
            4. Click the login button
            5. Verify that 'Welcome' text appears
            ```
            
            **🤖 Intelligent Features:**
            - **Smart Detection**: AI automatically detects if a page is a login screen, chat interface, or form
            - **Credential Management**: Use different usernames/passwords for different test cases
            - **Context-Aware Testing**: Automatically adapts testing approach based on page type
            - **Enhanced Chat Testing**: 
              * Detects ChatGPT, customer support, and general chat interfaces
              * Generates contextual questions like "What is the capital of France?" for AI assistants
              * Sends multiple test messages for comprehensive testing
              * Verifies responses to confirm chat functionality
            - **Login Testing**: Uses provided credentials for login screens, test data for other pages
            """)

# Testing options
st.markdown("### ⚙️ Testing Options")

col5, col6 = st.columns(2)

with col5:
    # Browser visibility option
    show_browser = st.checkbox(
        "👁️ Show browser window", 
        value=False,
        help="Watch the testing happen in real-time (slower but visual)"
    )
    
    if show_browser:
        st.info("Browser window will open - you can watch the testing!")

with col6:
    if testing_mode == "🔍 Automated Discovery Testing":
        explore_navigation = st.checkbox(
            "🧭 Explore multiple pages", 
            value=True,
            help="Navigate through links to test multiple pages"
        )
        
        # Only allow AI if model is loaded
        if model_loaded:
            use_ai = st.checkbox(
                "🤖 Use AI Intelligence", 
                value=False,
                help="Use Falcon model for smart testing (experimental)"
            )
        else:
            use_ai = False
            st.info("🤖 AI not available")
    else:
        explore_navigation = False
        use_ai = False
    
    show_detailed_logs = st.checkbox(
        "📋 Show detailed logs", 
        value=True,
        help="Display comprehensive test execution details"
    )

# Run button with validation
run_button_disabled = False
run_button_help = "Click to start testing"

if not url:
    run_button_disabled = True
    run_button_help = "Please enter a URL first"
elif needs_login and (not username or not password):
    run_button_disabled = True
    run_button_help = "Please provide login credentials"
elif testing_mode in ["📝 Custom Test Cases", "🔄 Both (Discovery + Custom)"] and not st.session_state.test_cases:
    run_button_disabled = True
    run_button_help = "Please upload an Excel file with test cases"

# Main execution button
if st.button(
    "🚀 Start Testing", 
    type="primary", 
    use_container_width=True,
    disabled=run_button_disabled,
    help=run_button_help
):
    # Progress tracking
    progress_bar = st.progress(0)
    status_text = st.empty()
    
    all_results = {}
    
    # Run Automated Discovery if selected
    if testing_mode in ["🔍 Automated Discovery Testing", "🔄 Both (Discovery + Custom)"]:
        status_text.text("🔍 Running automated discovery testing...")
        progress_bar.progress(0.3)
        
        discovery_results = run_universal_test(
            url=url,
            username=username,
            password=password,
            explore_nav=explore_navigation,
            use_ai=use_ai,
            show_browser=show_browser
        )
        
        all_results["discovery"] = discovery_results
        progress_bar.progress(0.6)
    
    # Run Custom Test Cases if selected
    if testing_mode in ["📝 Custom Test Cases", "🔄 Both (Discovery + Custom)"]:
        status_text.text("📝 Running custom test cases...")
        
        # Create test suite from session state
        test_suite = TestSuite("User Defined Test Cases")
        for tc in st.session_state.test_cases:
            test_case = TestCase(
                action=tc["action"],
                target=tc["target"],
                value=tc["value"],
                description=tc["description"],
                test_id=tc.get("test_id", ""),
                priority=tc.get("priority", "Medium"),
                username=tc.get("username", ""),
                password=tc.get("password", "")
            )
            test_suite.add_test(test_case)
        
        custom_results = run_test_cases(
            url=url,
            test_suite=test_suite,
            username=username,
            password=password,
            show_browser=show_browser,
            use_ai=model_loaded  # Use AI if model is available
        )
        
        all_results["custom"] = custom_results
        progress_bar.progress(0.9)
    
    
    progress_bar.progress(1.0)
    status_text.empty()
    
    # Show results
    st.success("✅ Testing completed!")
    
    # Show Discovery Results
    if "discovery" in all_results:
        st.markdown("### 🔍 Automated Discovery Results")
        
        results = all_results["discovery"]
        
        if results["success"]:
            col1, col2, col3 = st.columns(3)
            
            with col1:
                st.metric("Pages Tested", results["pages_tested"])
            with col2:
                st.metric("Elements Found", results["elements_found"])
            with col3:
                st.metric("Status", "✅ Success")
            
            # Element breakdown
            if results["test_summary"]:
                with st.expander("📊 Elements Discovered"):
                    col1, col2, col3, col4, col5 = st.columns(5)
                    
                    with col1:
                        st.metric("Buttons", results["test_summary"].get("buttons", 0))
                    with col2:
                        st.metric("Inputs", results["test_summary"].get("inputs", 0))
                    with col3:
                        st.metric("Links", results["test_summary"].get("links", 0))
                    with col4:
                        st.metric("Dropdowns", results["test_summary"].get("dropdowns", 0))
                    with col5:
                        st.metric("Clickables", results["test_summary"].get("clickables", 0))
            
            # Discovery logs
            if show_detailed_logs:
                with st.expander("📋 Discovery Test Logs"):
                    for log in results["logs"]:
                        if "✅" in log:
                            st.success(log)
                        elif "❌" in log:
                            st.error(log)
                        elif "⚠️" in log:
                            st.warning(log)
                        elif "=" in log[:5]:
                            st.markdown(f"**{log}**")
                        else:
                            st.write(log)
    
    # Show Custom Test Results
    if "custom" in all_results:
        st.markdown("### 📝 Custom Test Case Results")
        
        results = all_results["custom"]
        
        if results["success"]:
            # Summary metrics
            col1, col2, col3, col4 = st.columns(4)
            
            with col1:
                st.metric("Total Tests", results["summary"]["total"])
            with col2:
                st.metric("✅ Passed", results["summary"]["passed"])
            with col3:
                st.metric("❌ Failed", results["summary"]["failed"])
            with col4:
                pass_rate = (results["summary"]["passed"] / max(1, results["summary"]["total"])) * 100
                st.metric("Pass Rate", f"{pass_rate:.0f}%")
            
            # Individual test results
            st.markdown("#### Test Case Results:")
            
            for i, result in enumerate(results["test_results"], 1):
                if result["status"] == "PASS":
                    st.success(f"{i}. ✅ **{result['description']}** - {result['message']}")
                elif result["status"] == "FAIL":
                    st.error(f"{i}. ❌ **{result['description']}** - {result['message']}")
                else:
                    st.warning(f"{i}. ⚠️ **{result['description']}** - {result['message']}")
            
            # Test execution logs
            if show_detailed_logs:
                with st.expander("📋 Test Execution Logs"):
                    for log in results["logs"]:
                        if "✅" in log:
                            st.success(log)
                        elif "❌" in log:
                            st.error(log)
                        elif "⚠️" in log:
                            st.warning(log)
                        elif "=" in log[:5]:
                            st.markdown(f"**{log}**")
                        else:
                            st.write(log)
    
    
    # Export results
    st.markdown("### 💾 Export Results")
    
    # Create combined report
    report = "SELENIUM TEST REPORT\n"
    report += "=" * 50 + "\n"
    report += f"URL: {url}\n"
    report += f"Timestamp: {time.strftime('%Y-%m-%d %H:%M:%S')}\n"
    report += "=" * 50 + "\n\n"
    
    if "discovery" in all_results:
        report += "AUTOMATED DISCOVERY RESULTS\n"
        report += "-" * 30 + "\n"
        report += f"Pages Tested: {all_results['discovery']['pages_tested']}\n"
        report += f"Elements Found: {all_results['discovery']['elements_found']}\n\n"
    
    if "custom" in all_results:
        report += "CUSTOM TEST CASE RESULTS\n"
        report += "-" * 30 + "\n"
        report += f"Total: {all_results['custom']['summary']['total']}\n"
        report += f"Passed: {all_results['custom']['summary']['passed']}\n"
        report += f"Failed: {all_results['custom']['summary']['failed']}\n"
        report += f"Errors: {all_results['custom']['summary']['errors']}\n\n"
        
        report += "Test Cases:\n"
        for i, result in enumerate(all_results['custom']['test_results'], 1):
            report += f"{i}. [{result['status']}] {result['description']}: {result['message']}\n"
    
    
    st.download_button(
        label="📥 Download Test Report",
        data=report,
        file_name=f"test_report_{time.strftime('%Y%m%d_%H%M%S')}.txt",
        mime="text/plain"
    )

# Footer
st.markdown("---")
st.markdown("""
### 📚 How to Write Test Cases

**Basic Syntax:**
- **Action**: What to do (click, fill, verify, etc.)
- **Target**: Element to interact with (ID, XPath, CSS selector, or text)
- **Value**: Data for the action (text to fill, option to select, etc.)

**Examples:**
```
1. FILL "username" with "testuser"
2. FILL "password" with "pass123"  
3. CLICK "//button[@type='submit']"
4. VERIFY "Welcome" in page
5. WAIT for 2 seconds
```

**Target Formats:**
- `username` - Element ID
- `//button[text()='Login']` - XPath
- `.btn-primary` - CSS class
- `Sign In` - Link/button text

---

### 📝 Custom Test Cases (Natural Language Approach)

**Why Use Natural Language Test Scenarios?**
- ✅ **No technical knowledge required**: Write in plain English
- ✅ **AI-powered element discovery**: No need to know XPath or CSS selectors
- ✅ **Natural language descriptions**: Describe what you want to test
- ✅ **Step-by-step format**: Clear, numbered instructions
- ✅ **Professional results**: Get detailed test reports
- ✅ **Easy to share**: Send Excel files to team members

**How to Use Test Scenarios:**

1. **📥 Download Template**: Get the Test Scenarios template
2. **✏️ Write Test Scenarios**: Describe your tests in natural language
3. **📤 Upload File**: Upload the completed Excel file
4. **🚀 Run Tests**: AI automatically executes your scenarios

**Excel Template Columns:**

| Column | Description | Example |
|--------|-------------|---------|
| **Test_ID** | Unique identifier | TS001, TS002 |
| **Test_Name** | Brief description | "User Login", "Contact Form" |
| **Test_Description** | Step-by-step instructions | "1. Navigate to login page\\n2. Enter username..." |
| **Expected_Result** | What should happen | "User should be logged in successfully" |
| **Priority** | Importance level | High, Medium, Low |

**Writing Test Descriptions:**

Use natural language to describe what you want to test:

```
1. Navigate to the application homepage
2. Click on the login button
3. Enter 'testuser' in the username field
4. Enter 'Test123!' in the password field
5. Click the submit button
6. Wait for 2 seconds
7. Verify that 'Dashboard' text is displayed
```

**Key Benefits:**
- 🤖 **AI automatically finds elements** - No need to specify XPath or CSS selectors
- 📝 **Write in plain English** - Describe what you want to test
- 🎯 **Focus on test logic** - Not technical implementation details
- ✅ **Beginner-friendly** - Anyone can write test scenarios
- 🔄 **Reusable** - Save and share test scenarios across projects

**Tips for Success:**
- 📝 Write clear, step-by-step instructions
- 🎯 Set appropriate priorities (High for critical features)
- ✅ Include expected results for verification steps
- 🔍 Be specific about what to click or enter
- ⏱️ Include wait times when needed
""")